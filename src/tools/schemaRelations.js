import ModelService from "../services/modelServices.js";
import { constants } from '../constants/index.js';

const ObjectsToAvoid = ['iconImage', 'image', 'media', 'metaImage', 'variant', 'icon'];

export const removeIdField = (obj) => {
    if (Array.isArray(obj)) {
        return obj.map(removeIdField);
    } else if (typeof obj === 'object' && obj !== null) {
        const newObj = {};
        for (const key in obj) {
            if (ObjectsToAvoid.includes(key)) {
                newObj[key] = obj[key];
            } else if (key !== 'id') {
                newObj[key] = removeIdField(obj[key]);
            }
        }
        return newObj;
    }
    return obj;
}

export const removeFieldFIeld = (obj, fieldToRemove) => {
    if (Array.isArray(obj)) {
        return obj.map((o) => removeFieldFIeld(o, fieldToRemove));
    } else if (typeof obj === 'object' && obj !== null) {
        const newObj = {};
        for (const key in obj) {
            if (ObjectsToAvoid.includes(key)) {
                newObj[key] = obj[key];
            } else if (key !== fieldToRemove) {
                newObj[key] = removeFieldFIeld(obj[key], fieldToRemove);
            }
        }
        return newObj;
    }
    return obj;
}

export const apiModelToPluralModel = (apiModel) => {
    const [api, model] = apiModel.split('::');
    const [singularModel] = model.split('.');
    const alreadyPlural = singularModel[singularModel.length - 1] === 's';
    return `${singularModel}${alreadyPlural ? '' : 's'}`;
}

export const findRelations = ({
    object,
    schema
}) => {
    // console.log('findRelations schema:', JSON.stringify(schema.attributes, null, 2));
    const relationFields = [];
    for (const field in schema.attributes) {
        if (field === 'customComponents') {
            continue;
        }
        const type = schema.attributes[field].type;
        const repeatable = schema.attributes[field].repeatable;
        // console.log('field:', JSON.stringify(field, null, 2));
        if (['relation'].includes(type)) {
            relationFields.push({
                field: {
                    ...schema.attributes[field],
                    name: field
                },
                relatedElements: object[field]?.data || null
            });
        } else if (['component'].includes(type)) {
            relationFields.push({
                field: {
                    ...schema.attributes[field],
                    name: field
                },
                relatedElements: repeatable
                    ? object[field]?.map(({ data }) => data) || []
                    : object[field]?.data || {}
            });
        }
    }
    return relationFields;
};

// TODO improve this function to find the localizations without using strapi object
export const findAndSetRelatedLocalizations = async ({
    relationFields,
    translatedLocale,
    propsToUpdate
}) => {
    for (const { field, relatedElements } of relationFields) {
        propsToUpdate[field.name] = null;
        // console.log('field:', JSON.stringify(field, null, 2));
        // console.log('relatedElements:', JSON.stringify(relatedElements, null, 2));
        if (!relatedElements) {
            continue;
        }

        if (Array.isArray(relatedElements)) {
            const newRelationField = [];
            for (const relatedElement of relatedElements) {
                if (relatedElement) {
                    const relatedElementWithLocalizations = await strapi.entityService?.findOne(
                        field.target,
                        relatedElement.id,
                        {
                            populate: ['localizations']
                        }
                    );
                    // console.log('relatedElementWithLocalizations:', JSON.stringify(relatedElementWithLocalizations, null, 2));

                    const localizedElement = relatedElementWithLocalizations?.localizations?.find(
                        (loc) => loc.locale === translatedLocale
                    );
                    // console.log('localizedElement:', JSON.stringify(localizedElement, null, 2));

                    if (localizedElement) {
                        newRelationField.push({
                            id: localizedElement.id
                        });
                    }
                }
            }
            propsToUpdate[field.name] = {
                set: newRelationField
            };
        } else {
            if (relatedElements) {
                if (field.name === 'variant') {
                    propsToUpdate[field.name] = {
                        set: [{ id: relatedElements.id }]
                    };
                    continue;
                }

                const relatedElementWithLocalizations = await strapi.entityService?.findOne(
                    field.target,
                    relatedElements.id,
                    {
                        populate: ['localizations']
                    }
                );
                // console.log('relatedElementWithLocalizations:', JSON.stringify(relatedElementWithLocalizations, null, 2));

                const localizedElement = relatedElementWithLocalizations?.localizations?.find(
                    (loc) => loc.locale === translatedLocale
                );
                // console.log('localizedElement:', JSON.stringify(localizedElement, null, 2));

                if (localizedElement) {
                    propsToUpdate[field.name] = {
                        set: [{ id: localizedElement.id }]
                    };
                }
            }
        }
    }
}

export const findAndSetRelatedElement = async ({
    relationFields,
    propsToUpdate
}) => {
    for (const { field, relatedElements } of relationFields) {
        const fieldType = field.type;
        const repeatable = field.repeatable;

        propsToUpdate[field.name] = null;
        console.log('field:', JSON.stringify(field, null, 2));
        console.log('relatedElements:', JSON.stringify(relatedElements, null, 2));
        if (!relatedElements) {
            continue;
        }

        if (fieldType === 'relation') {
            const modelName = field.target ? apiModelToPluralModel(field.target) : field.component;
            const modelService = new ModelService({
                url: constants.destination.url,
                token: constants.destination.token,
                modelName
            })

            if (Array.isArray(relatedElements)) {
                const newRelationField = [];
                for (const { id, attributes: { pages, ...relatedElement } } of relatedElements) {
                    if (relatedElement) {
                        const filters = {}
                        if (relatedElement.title) {
                            filters.title = relatedElement.title;
                        } else if (relatedElement.code) {
                            filters.code = relatedElement.code;
                        } else if (relatedElement.name) {
                            filters.name = relatedElement.name;
                        }
                        const { data } = await modelService.getElements(filters);
                        let relatedElementFound = data;

                        if (!relatedElementFound) {
                            const { data } = await modelService.createElement(
                                id,
                                relatedElement
                            );
                            relatedElementFound = data;
                        }

                        if (relatedElementFound) {
                            newRelationField.push({
                                id: relatedElementFound.id
                            });
                        }
                    }
                }
                propsToUpdate[field.name] = {
                    set: newRelationField
                };
            } else {
                if (relatedElements) {
                    const { id, attributes: { pages, ...relatedElement } } = relatedElements;
                    if (['media'].includes(field.name)) {
                        propsToUpdate[field.name] = null;
                        continue;
                    }

                    /* if (['variant', 'footer', 'navbar'].includes(field.name)) {
                        propsToUpdate[field.name] = {
                            set: [{ id }]
                        };
                        continue;
                    }
                    */
                    const filters = {}
                    if (relatedElement.title) {
                        filters.title = relatedElement.title;
                    } else if (relatedElement.code) {
                        filters.code = relatedElement.code;
                    } else if (relatedElement.name) {
                        filters.name = relatedElement.name;
                    }
                    const { data } = await modelService.getElements(filters);
                    let [relatedElementFound] = data;
                    console.log('relatedElementFound: ', JSON.stringify(relatedElementFound, null, 2));

                    if (!relatedElementFound) {
                        const { data } = await modelService.createElement(
                            relatedElement
                        );
                        relatedElementFound = data;
                    }

                    if (relatedElementFound) {
                        propsToUpdate[field.name] = {
                            set: [{
                                id: relatedElementFound.id
                            }]
                        };
                    } else {
                        propsToUpdate[field.name] = null;
                    }
                }
            }
        } else if (fieldType === 'component') {
            if (repeatable) {
                const value = relatedElements?.filter((e)=> !!e).map(({ attributes }) => attributes) || []
                propsToUpdate[field.name] = [...value]
            } else {
                const value = relatedElements?.data?.attributes || null
                propsToUpdate[field.name] = value;
            }
            continue;
        }
    }
}