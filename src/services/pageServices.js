import strapi from './strapi';

const CONTENT_TYPE = 'pages';

export const getPage = async (id) => {
    const response = await strapi.getElement(id, CONTENT_TYPE);
    return response
}

export const getPages = async (filters) => {
    const response = await strapi.getElements(CONTENT_TYPE, filters);
    return response
}

export const createPage = async (data) => {
    const response = await strapi.createElement(CONTENT_TYPE, data);
    return response
}

export const updatePage = async (id, data) => {
    const response = await strapi.updateElement(CONTENT_TYPE, id, data);
    return response
}
