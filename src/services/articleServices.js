import StrapiService from './strapi.js';

const CONTENT_TYPE = 'news-posts';

class ArticleService extends StrapiService {
    constructor({ url, token }) {
        super({ url, token });
    }

    async getArticle(id, config) {
        const response = await this.getElement(id, CONTENT_TYPE, config);
        return response
    }

    async getArticles(filters, config) {
        const response = await this.getElements(CONTENT_TYPE, {
            filters,
            pagination: {
                start: 0,
                limit: 1000
            },
            sort: 'title:asc',
            locale: 'all',
            publicationState: 'preview',
            ...config,
        });
        return response
    }

    async createArticle(data, config) {
        const response = await this.createElement(CONTENT_TYPE, data, config);
        return response
    }

    async updateArticle(id, data, config) {
        const response = await this.updateElement(CONTENT_TYPE, id, data, config);
        return response
    }
}
export default ArticleService;
