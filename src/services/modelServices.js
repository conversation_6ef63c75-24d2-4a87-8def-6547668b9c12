import StrapiService from './strapi.js';

class ModelService {
    modelName = '';
    service = new StrapiService({});

    constructor({ url, token, modelName }) {
        this.modelName = modelName;
        this.service = new StrapiService({ url, token });
    }

    async getElement(id, config) {
        const response = await this.service.getElement(id, this.modelName, config);
        return response
    }

    async getElements(filters, config) {
        const response = await this.service.getElements(this.modelName,
            {
                ...filters,
            },
            {
                ...config,
            }
        );
        return response
    }

    async createElement(data, config) {
        console.log('modelName: ', this.modelName);
        console.log('data: ', JSON.stringify(data, null, 2));
        const response = await this.service.createElement(this.modelName, data, config);
        return response
    }

    async updateElement(id, data, config) {
        const response = await this.service.updateElement(id, this.modelName, data, config);
        return response
    }

    async deleteElement(id, config) {
        const response = await this.service.deleteElement(id, this.modelName, config);
        return response
    }
}
export default ModelService;
