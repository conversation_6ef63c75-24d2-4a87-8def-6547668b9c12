import axios from 'axios';

class SitefinityMediaService {
    constructor({ baseUrl, apiKey }) {
        this.baseUrl = baseUrl;
        this.apiKey = apiKey;
        this.client = axios.create({
            baseURL: baseUrl,
            headers: {
                'Authorization': `Basic ${apiKey}`,
                'Content-Type': 'application/json'
            }
        });
    }

    async getMediaById(mediaId) {
        try {
            const response = await this.client.get(`/api/default/images(${mediaId})`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching media with ID ${mediaId}:`, error);
            throw error;
        }
    }

    async getMediaBatch(mediaIds) {
        try {
            const mediaPromises = mediaIds.map(id => this.getMediaById(id));
            const results = await Promise.all(mediaPromises);
            return results;
        } catch (error) {
            console.error('Error fetching media batch:', error);
            throw error;
        }
    }

    async downloadMedia(mediaUrl) {
        try {
            const response = await axios.get(mediaUrl, {
                responseType: 'arraybuffer'
            });
            return response.data;
        } catch (error) {
            console.error('Error downloading media:', error);
            throw error;
        }
    }
}

export default SitefinityMediaService; 