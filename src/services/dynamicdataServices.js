import StrapiService from './strapi.js';

const CONTENT_TYPE = 'nuggets';

class DynamicDataService {
    service = new StrapiService({});

    constructor({ url, token }) {
        this.service = new StrapiService({ url, token });
    }

    async getElement(id, config) {
        const response = await this.service.getElement(id, CONTENT_TYPE, config);
        return response
    }

    async getElements(filters, config) {
        const response = await this.service.getElements(CONTENT_TYPE,
            {
                ...filters,
            },
            {
                ...config,
            }
        );
        return response
    }

    async createElement(data, config) {
        const response = await this.service.createElement(CONTENT_TYPE, data, config);
        return response
    }

    async updateElement(id, data, config) {
        const response = await this.service.updateElement(CONTENT_TYPE, id, data, config);
        return response
    }
}
export default DynamicDataService;
