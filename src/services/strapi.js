import Strapi from "strapi-sdk-js"

class StrapiService {
    strapi

    constructor({ url, token }) {
        this.strapi = new Strapi({
            url,
            prefix: "/api",
            store: {
                key: "strapi_jwt",
                useLocalStorage: false,
                cookieOptions: { path: "/" },
            },
            axiosOptions: {},
        });
        this.strapi.axios.defaults.headers.common["Authorization"] = `Bearer ${token}`;
    }

    async getElement(id, contentType, config) {
        const response = await this.strapi.findOne(contentType, id, {
            ...config,
        });
        return response
    }

    async getElements(contentType, filters, config) {
        try {
            const configs = {
                filters,
                ...config
            }
            const response = await this.strapi.find(contentType, configs);
            return response
        } catch (error) {
            console.error('getElements error:', error);
            return [];
        }
    }

    async createElement(contentType, data, config) {
        const response = await this.strapi.create(contentType,
            {
                ...data,
            },
            {
                ...config
            }
        );
        return response
    }

    async updateElement(id, contentType, data, config) {
        const response = await this.strapi.update(contentType, id,
            {
                ...data,
            },
            {
                ...config
            }
        );
        return response
    }

    async deleteElement(id, contentType) {
        const response = await this.strapi.delete(contentType, id);
        return response
    }

}
export default StrapiService;


