import { constants } from '../constants/index.js';
import StaticContentService from "../services/staticContentServices.js";

export const processStaticContent = async (source) => {
    console.info('Starting static content processing...');
    const sourceStrapi = new StaticContentService({
        url: source.url,
        token: source.token,
    });

    let page = 1;
    let pageCount = 2;
    const elements = [];
    
    // Fetch all static content items with pagination
    while (page <= pageCount) {
        console.log(`Fetching page: ${page} of ${pageCount}`);
        let { meta, data } = await sourceStrapi.getElements(
            {},
            {
                pagination: {
                    page,
                    pageSize: 100
                },
                populate: ["deep"]
            }
        );
        elements.push(...data);
        pageCount = meta.pagination.pageCount;
        page++;
    }

    const elementsFoundLength = elements.length;
    console.log(`Found ${elementsFoundLength} static content elements to process`);
    
    // Process each static content item
    let processedCount = 0;
    for (const element of elements) {
        try {
            console.log(`Processing static content ${element.id}: ${element.attributes?.title || 'Untitled'}`);
            
            // Add your processing logic here
            // For example, you might want to:
            // - Update content
            // - Sanitize fields
            // - Transform data
            
            // Example: Update a field if needed
            // const updatedData = {
            //     content: processContent(element.attributes?.content)
            // };
            // await sourceStrapi.updateElement(element.id, updatedData);
            
            processedCount++;
        } catch (error) {
            console.error(`Error processing static content ${element.id}:`, error);
        }
    }
    
    console.log(`Processing complete. Processed ${processedCount} static content items.`);
}

// Add any helper functions you need for processing
// function processContent(content) {
//     // Your processing logic here
//     return content;
// }

// Execute the script
processStaticContent({
    url: constants.source.url,
    token: constants.source.token,
});