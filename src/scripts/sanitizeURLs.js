import { constants } from '../constants/index.js';
import URLService from "../services/urlServices.js";

export const sanitizeURLs = async (source) => {
    console.info('Starting URL sanitization process...');
    const sourceStrapi = new URLService({
        url: source.url,
        token: source.token,
    });

    let page = 1;
    let pageCount = 2;
    const elements = [];

    // Fetch all URLs with pagination
    while (page <= pageCount) {
        console.log(`Fetching page: ${page} of ${pageCount}`);
        let { meta, data } = await sourceStrapi.getElements(
            {

            },
            {
                locale: 'de',
                pagination: {
                    page,
                    pageSize: 100
                }
            }
        );
        elements.push(...data);
        pageCount = meta.pagination.pageCount;
        page++;
    }

    const elementsFoundLength = elements.length;
    console.log(`Found ${elementsFoundLength} URL elements to process`);

    // Process each URL
    let sanitizedCount = 0;
    for (const element of elements) {
        try {
            // console.log('element: ', element.attributes);
            const suffixUrl = element.attributes?.suffixURL;
            if (!suffixUrl) continue;

            console.log(`Processing URL ${element.id}: ${suffixUrl}`);

            // Sanitize the suffixUrl - remove special characters and normalize
            const sanitizedSuffix = sanitizeString(suffixUrl);

            // Only update if the sanitized version is different
            if (sanitizedSuffix !== suffixUrl) {
                console.log(`Sanitizing: "${suffixUrl}" → "${sanitizedSuffix}"`);

                await sourceStrapi.updateElement(element.id, { suffixURL: sanitizedSuffix });

                sanitizedCount++;
            }
        } catch (error) {
            console.error(`Error processing URL ${element.id}:`, error);
        }
    }

    console.log(`Sanitization complete. Updated ${sanitizedCount} URLs.`);
}

// Function to sanitize strings by removing special characters
function sanitizeString(str) {
    if (!str) return str;

    // Replace accented characters with their non-accented equivalents
    return str.normalize("NFD")
        .replace(/[\u0300-\u036f]/g, "") // Remove diacritics
        .replace(/[^\w\s-]/g, "") // Remove special characters except whitespace and hyphens
        .replace(/\s+/g, "-") // Replace spaces with hyphens
        .replace(/-+/g, "-") // Replace multiple hyphens with a single hyphen
        .trim(); // Remove leading/trailing whitespace
}

// Execute the script
sanitizeURLs({
    url: constants.source.url,
    token: constants.source.token,
});