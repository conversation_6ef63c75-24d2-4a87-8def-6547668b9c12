import { constants } from '../constants/index.js';
import StaticContentService from "../services/staticContentServices.js";

export const cleanStaticContentURLs = async (source) => {
    console.info('Starting static content URL cleaning process...');
    const sourceStrapi = new StaticContentService({
        url: source.url,
        token: source.token,
    });

    let page = 1;
    let pageCount = 2;
    const elements = [];
    
    // Fetch all static content items with pagination
    while (page <= pageCount) {
        console.log(`Fetching page: ${page} of ${pageCount}`);
        let { meta, data } = await sourceStrapi.getElements(
            {},
            {
                pagination: {
                    page,
                    pageSize: 100
                },
                populate: ["deep"]
            }
        );
        elements.push(...data);
        pageCount = meta.pagination.pageCount;
        page++;
    }

    const elementsFoundLength = elements.length;
    console.log(`Found ${elementsFoundLength} static content elements to process`);
    
    // Process each static content item
    let removedCount = 0;
    let itemsWithSpecialChars = [];
    
    for (const element of elements) {
        try {
            const title = element.attributes?.title || 'Untitled';
            const url = element.attributes?.url;
            
            console.log(`Checking static content ${element.id}: ${title}`);
            
            if (url && containsSpecialCharacters(url)) {
                console.log(`Found URL with special characters: ${url}`);
                itemsWithSpecialChars.push({
                    id: element.id,
                    title: title,
                    url: url
                });
                
                // Delete the element
                await sourceStrapi.deleteElement(element.id);
                console.log(`Removed static content with ID: ${element.id}`);
                removedCount++;
            }
        } catch (error) {
            console.error(`Error processing static content ${element.id}:`, error);
        }
    }
    
    // Log summary of removed items
    console.log(`\n--- Cleaning Summary ---`);
    console.log(`Total items processed: ${elementsFoundLength}`);
    console.log(`Items removed due to special characters in URLs: ${removedCount}`);
    
    if (itemsWithSpecialChars.length > 0) {
        console.log(`\nRemoved items:`);
        itemsWithSpecialChars.forEach(item => {
            console.log(`- ID: ${item.id}, Title: ${item.title}, URL: ${item.url}`);
        });
    }
    
    console.log(`\nCleaning process complete.`);
}

// Function to check if a string contains special characters
function containsSpecialCharacters(str) {
    if (!str) return false;
    
    // Normalize the string and check for special characters
    const normalized = str.normalize("NFD");
    
    // Check for diacritics and special characters (excluding hyphens and underscores)
    const hasSpecialChars = /[\u0300-\u036f]|[^\w\s\-_]/g.test(normalized);
    
    return hasSpecialChars;
}

// Execute the script
cleanStaticContentURLs({
    url: constants.source.url,
    token: constants.source.token,
});