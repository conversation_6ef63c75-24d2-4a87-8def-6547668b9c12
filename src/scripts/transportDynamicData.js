import { constants } from '../constants/index.js';
import DynamicDataService from "../services/dynamicdataServices.js"

export const transportElements = async (source, destination) => {
    const sourceStrapi = new DynamicDataService({
        url: source.url,
        token: source.token,
    });
    const { data } = await sourceStrapi.getElements(
        {},
        {
            pagination: {
                page: 1,
                pageSize: 100
            }
        }
    );
    // console.log('elementsFound: ', data)
    const elementsFoundLength = data.length;
    let count = 0;
    console.log(`${elementsFoundLength} elements found`);

    const destinationStrapi = new DynamicDataService({
        url: destination.url,
        token: destination.token,
    });
    for (const element of data) {
        try {
            console.log(`Start processing ${count++} from ${elementsFoundLength}: ${element.attributes?.name}...`);
            const { data } = await destinationStrapi.getElements(
                {
                    name: element.attributes.name
                },
                {
                    pagination: {
                        page: 1,
                        pageSize: 1
                    },
                }
            );
            const elementFound = data[0];
            console.log('elementFound: ', elementFound?.attributes?.name);
            if (!elementFound) {
                const elementCreated = await destinationStrapi.createElement({ id: element.id, ...element.attributes });
                console.log('elementCreated: ', elementCreated);
            }
        } catch (error) {
            console.error(`${element.attributes.name} failed`, error);
        }
    }
}

transportElements(
    {
        url: constants.source.url,
        token: constants.source.token,
    },
    {
        url: constants.destination.url,
        token: constants.destination.token,
    }
);
