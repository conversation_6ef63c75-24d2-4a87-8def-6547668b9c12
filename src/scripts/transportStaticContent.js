import { constants } from '../constants/index.js';
import StaticContentService from "../services/staticContentServices.js";

export const transportStaticContent = async (source, destination) => {
    console.info('Starting static content transport...');
    const sourceStrapi = new StaticContentService({
        url: source.url,
        token: source.token,
    });

    let page = 1;
    let pageCount = 2;
    const elements = [];
    
    // Fetch all static content items with pagination
    while (page <= pageCount) {
        console.log(`Fetching page: ${page} of ${pageCount}`);
        let { meta, data } = await sourceStrapi.getElements(
            {},
            {
                pagination: {
                    page,
                    pageSize: 100
                },
                populate: ["deep"]
            }
        );
        elements.push(...data);
        pageCount = meta.pagination.pageCount;
        page++;
    }

    const elementsFoundLength = elements.length;
    let count = 0;
    console.log(`${elementsFoundLength} static content elements found`);

    const destinationStrapi = new StaticContentService({
        url: destination.url,
        token: destination.token,
    });
    
    for (const element of elements) {
        try {
            console.log(`Start processing ${count++} from ${elementsFoundLength}: ${element.attributes?.title || 'Untitled'}...`);
            const { data } = await destinationStrapi.getElements(
                {
                    title: element.attributes?.title
                },
                {
                    pagination: {
                        page: 1,
                        pageSize: 1
                    },
                }
            );
            const elementFound = data[0];
            console.log('elementFound: ', elementFound?.attributes?.title);
            
            if (!elementFound) {
                const elementCreated = await destinationStrapi.createElement({ id: element.id, ...element.attributes });
                console.log('elementCreated: ', elementCreated);
            }
        } catch (error) {
            console.error(`${element.attributes?.title || 'Untitled'} failed`, error);
        }
    }
}

// Execute the script
transportStaticContent(
    {
        url: constants.source.url,
        token: constants.source.token,
    },
    {
        url: constants.destination.url,
        token: constants.destination.token,
    }
);