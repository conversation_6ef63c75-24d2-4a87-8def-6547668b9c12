import { constants } from './src/constants/index.js';
import VariantService from "./src/services/variantServices.js"

export const transportElements = async (source, destination) => {
    const sourceStrapi = new VariantService({
        url: source.url,
        token: source.token,
    });
    const { data } = await sourceStrapi.getElements({}, {
        limit: 1000,
        populate: ["deep"]
    });
    console.log('elementsFound: ', data)
    const elementsFoundLength = data.length;
    let count = 0;
    console.log(`${elementsFoundLength} elements found`);

    const destinationStrapi = new VariantService({
        url: destination.url,
        token: destination.token,
    });
    for (const element of data) {
        try {
            console.log(`Start processing ${count++} from ${elementsFoundLength}: ${element.attributes?.name}...`);
            const { data } = await destinationStrapi.getElements({
                code: element.attributes?.code
            }, {
                limit: 1
            });
            const elementFound = data[0];
            if (!elementFound) {
                const elementCreated = await destinationStrapi.createElement({ id: element.id, ...element.attributes });
            }
        } catch (error) {
            console.error(`${element.attributes.name} failed`, error);
        }
    }
}

transportElements(
    {
        url: constants.source.url,
        token: constants.source.token,
    },
    {
        url: constants.destination.url,
        token: constants.destination.token,
    }
);
