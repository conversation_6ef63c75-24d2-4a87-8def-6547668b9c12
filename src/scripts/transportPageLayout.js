import { constants } from '../constants/index.js';
import PageLayoutService from "../services/pagelayoutServices.js"
import { findAndSetRelatedElement, findRelations, removeFieldFIeld } from '../tools/schemaRelations.js';

export const transportElements = async (source, destination) => {
    console.info('transportElements...')
    const sourceStrapi = new PageLayoutService({
        url: source.url,
        token: source.token,
    });

    let page = 1;
    let pageCount = 2;
    const elements = []
    while (page <= pageCount) {
        console.log('page: ', page, '; pageCount: ', pageCount);
        let { meta, data } = await sourceStrapi.getElements(
            {
                /* id: {
                    $in: [18]
                } */
            },
            {
                pagination: {
                    page,
                    pageSize: 1
                },
                populate: ["deep"]
            }
        );
        console.log('meta: ', meta);
        elements.push(...data);
        pageCount = meta.pagination.pageCount;
        page++;
        break;
    }

    const elementsFoundLength = elements.length;
    let count = 0;
    console.log(`${elementsFoundLength} elements found`);

    const destinationStrapi = new PageLayoutService({
        url: destination.url,
        token: destination.token,
    });
    for (const element of elements) {
        try {
            console.log(`Start processing ${count++} from ${elementsFoundLength}: ${element.attributes?.title}...`);
            const { data } = await destinationStrapi.getElements(
                {
                    title: element.attributes?.title
                },
                {
                    pagination: {
                        page: 1,
                        pageSize: 1
                    },
                }
            );
            const elementFound = data[0];
            console.log('elementFound: ', elementFound?.attributes?.title);

            if (!elementFound) {
                // console.log('element: ', JSON.stringify(element, null, 2));
                let { pages, ...attributes } = element.attributes;
                attributes = removeFieldFIeld(attributes, 'pages');
                const relationsFound = findRelations({
                    object: attributes,
                    schema: constants.schemaLayouts['page-layouts'].layout
                });
                let newElement = {
                    ...attributes,
                }
                await findAndSetRelatedElement({
                    relationFields: relationsFound,
                    propsToUpdate: newElement,
                })
                const newContent = [];
                for (const { id, ...section } of attributes?.content || []) {
                    const schema = constants.schemaLayouts['page-layouts'].allLayoutData.components[section.__component];
                    const sectionRelations = findRelations({
                        object: section,
                        schema,
                    })
                    await findAndSetRelatedElement({
                        relationFields: sectionRelations,
                        propsToUpdate: section,
                    })
                    newContent.push(section);
                }
                newElement.content = newContent;
                console.log('newElement: ', JSON.stringify(newElement, null, 2));
                const elementCreated = await destinationStrapi.createElement({ id: element.id, ...newElement });
                console.log('elementCreated: ', elementCreated);
            }
        } catch (error) {
            console.error(`${element.attributes.name} failed`, error);
        }
    }
}

transportElements(
    {
        url: constants.source.url,
        token: constants.source.token,
    },
    {
        url: constants.destination.url,
        token: constants.destination.token,
    }
);
