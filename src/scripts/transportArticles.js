import ArticleService from "./src/services/articleServices.js"

export const transportArticles = async (source, destination) => {
    const sourceStrapi = new ArticleService({
        url: source.url,
        token: source.token,
    });
    const { data } = await sourceStrapi.getArticles({}, {
        limit: 1000,
        populate: ["deep"]
    });
    console.log('articlesFound: ', data)
    const articlesFoundLength = data.length;
    let count = 0;
    console.log(`${articlesFoundLength} articles found`);

    const destinationStrapi = new ArticleService({
        url: destination.url,
        token: destination.token,
    });
    for (const article of data) {
        try {
            console.log(`Start processing ${count} from ${articlesFoundLength}: ${article.name}...`);
            const articleFound = await destinationStrapi.getArticles({
                name: article.name
            }, {
                limit: 1
            });
            if (!articleFound) {
                await destinationStrapi.createArticle(article);
            }
        } catch (error) {
            console.error(`${article.name} failed`, error);
        }
    }
}

transportArticles(
    {
        url: 'http://127.0.0.1:1337',
        token: '7eae1ea55c2639a8a67301ef936539e2440fda68995ee76c125b523640b7736c4395c2f1f73e1ceef0629669010cb31191c3f6dc28b4dd299fd9d2379d1b57dd66711da8f0ff2d6b02396cfb49c290f8d3192b2148785a5dd77cc233ee94fb779c8de3cc0ea2415ad9465092547cccd457e3d41c422a40009643bc106a138e31'
    },
    {
        url: 'http://127.0.0.1:1337',
        token: '7eae1ea55c2639a8a67301ef936539e2440fda68995ee76c125b523640b7736c4395c2f1f73e1ceef0629669010cb31191c3f6dc28b4dd299fd9d2379d1b57dd66711da8f0ff2d6b02396cfb49c290f8d3192b2148785a5dd77cc233ee94fb779c8de3cc0ea2415ad9465092547cccd457e3d41c422a40009643bc106a138e31'
    }
);
/* {
    url: 'https://devcms.sendvalu.com',
    token: 'b4c08745a937aaa0e7b21d7ca04e4f01ad866664e4016e647d0f36e5d57cfce9dee8825eaefe93696c1727728017410082da769e8490fcfea86fcab3965ab62ac59f095f586d2da7b9853e59ff1761e69117731370290ccc786ff89a916478eec132a1e1a6d2dd2f806df02b29c70485a50afaa708a72d019290aa7dd2dba66f'
}, */