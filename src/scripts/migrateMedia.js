import SitefinityMediaService from '../services/sitefinityMediaServices.js';
import StrapiService from '../services/strapi.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.dev' });

async function migrateMedia(mediaIds) {
    try {
        // Initialize services
        const sitefinityService = new SitefinityMediaService({
            baseUrl: process.env.SITEFINITY_BASE_URL,
            apiKey: process.env.SITEFINITY_API_KEY
        });

        const strapiService = new StrapiService({
            url: process.env.STRAPI_URL,
            token: process.env.STRAPI_TOKEN
        });

        // Get media information from Sitefinity
        console.log('Fetching media information from Sitefinity...');
        const mediaItems = await sitefinityService.getMediaBatch(mediaIds);

        // Process each media item
        for (const mediaItem of mediaItems) {
            try {
                console.log(`Processing media: ${mediaItem.Title}`);

                // Download the media file
                const mediaData = await sitefinityService.downloadMedia(mediaItem.Url);

                // Create FormData for Strapi upload
                const formData = new FormData();
                formData.append('files', new Blob([mediaData]), mediaItem.Title);

                // Upload to Strapi
                const uploadResponse = await strapiService.strapi.axios.post('/api/upload', formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                });

                console.log(`Successfully migrated media: ${mediaItem.Title}`);
                console.log('Strapi response:', uploadResponse.data);

            } catch (error) {
                console.error(`Error processing media ${mediaItem.Title}:`, error);
                // Continue with next media item even if one fails
                continue;
            }
        }

        console.log('Media migration completed');

    } catch (error) {
        console.error('Media migration failed:', error);
        throw error;
    }
}

// Example usage
const mediaIds = process.argv.slice(2);
if (mediaIds.length === 0) {
    console.error('Please provide media IDs as command line arguments');
    process.exit(1);
}

migrateMedia(mediaIds)
    .then(() => process.exit(0))
    .catch(() => process.exit(1)); 