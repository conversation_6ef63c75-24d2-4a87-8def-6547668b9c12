import { constants } from '../constants/index.js';
import StaticContentService from "../services/staticContentServices.js";

export const sanitizeStaticContentURLs = async (source) => {
    console.info('Starting static content URL sanitization process...');
    const sourceStrapi = new StaticContentService({
        url: source.url,
        token: source.token,
    });

    let page = 1;
    let pageCount = 2;
    const elements = [];

    // Fetch all static content items with pagination
    while (page <= pageCount) {
        console.log(`Fetching page: ${page} of ${pageCount}`);
        let { meta, data } = await sourceStrapi.getElements(
            {},
            {
                locale: 'pt',
                pagination: {
                    page,
                    pageSize: 100
                },
                populate: ["deep"]
            }
        );
        elements.push(...data);
        pageCount = meta.pagination.pageCount;
        page++;
    }

    const elementsFoundLength = elements.length;
    console.log(`Found ${elementsFoundLength} static content elements to process`);

    // Process each static content item
    let sanitizedCount = 0;

    for (const element of elements) {
        try {
            const url = element.attributes?.url;
            if (!url) continue;

            // console.log(`Processing static content ${element.id}: ${element.attributes?.title || 'Untitled'}`);
            // console.log(`Checking URL: ${url}`);

            // Extract the last part of the URL (country name part)
            const urlParts = url.split('/');
            const countryPart = urlParts[urlParts.length - 1];

            // Only process if the country part exists
            if (countryPart) {
                // Sanitize only the country part
                const sanitizedCountryPart = sanitizeString(countryPart);

                // Only update if the sanitized version is different
                if (sanitizedCountryPart !== countryPart) {
                    // Replace the last part with the sanitized version
                    urlParts[urlParts.length - 1] = sanitizedCountryPart;
                    const sanitizedUrl = urlParts.join('/');

                    console.log(`Sanitizing URL: "${url}" → "${sanitizedUrl}"`);

                    // await sourceStrapi.updateElement(element.id, { url: sanitizedUrl });

                    sanitizedCount++;
                }
            }
        } catch (error) {
            console.error(`Error processing static content ${element.id}:`, error);
        }
    }

    console.log(`Sanitization complete. Updated ${sanitizedCount} static content items.`);
}

// Function to sanitize strings by removing special characters
function sanitizeString(str) {
    if (!str) return str;

    // Replace accented characters with their non-accented equivalents
    return str.normalize("NFD")
        .replace(/[\u0300-\u036f]/g, "") // Remove diacritics (ä→a, é→e, ñ→n, etc.)
        .replace(/[^\w\s\-_]/g, "") // Remove special characters except alphanumeric, whitespace, hyphens, and underscores
        .replace(/\s+/g, "-") // Replace spaces with hyphens
        .replace(/ß/g, "ss") // Replace German eszett with 'ss'
        .replace(/æ/g, "ae") // Replace æ with 'ae'
        .replace(/ø/g, "o") // Replace ø with 'o'
        .replace(/å/g, "a") // Replace å with 'a'
        .replace(/œ/g, "oe") // Replace œ with 'oe'
        .replace(/-+/g, "-") // Replace multiple hyphens with a single hyphen
        .toLowerCase() // Convert to lowercase for consistency
        .trim(); // Remove leading/trailing whitespace
}

// Execute the script
sanitizeStaticContentURLs({
    url: constants.source.url,
    token: constants.source.token,
});
