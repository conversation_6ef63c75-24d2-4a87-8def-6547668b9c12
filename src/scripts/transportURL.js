import { constants } from '../constants/index.js';
import URLService from "../services/urlServices.js"

export const transportElements = async (source, destination) => {
    console.info('transportElements...')
    const sourceStrapi = new URLService({
        url: source.url,
        token: source.token,
    });

    let page = 1;
    let pageCount = 2;
    const elements = []
    while (page <= pageCount) {
        console.log('page: ', page, '; pageCount: ', pageCount);
        let { meta, data } = await sourceStrapi.getElements(
            {},
            {
                pagination: {
                    page,
                    pageSize: 100
                },
                populate: ["deep"]
            }
        );
        console.log('meta: ', meta);
        elements.push(...data);
        pageCount = meta.pagination.pageCount;
        page++;
    }

    const elementsFoundLength = elements.length;
    let count = 0;
    console.log(`${elementsFoundLength} elements found`);

    const destinationStrapi = new URLService({
        url: destination.url,
        token: destination.token,
    });
    for (const element of elements) {
        try {
            console.log(`Start processing ${count++} from ${elementsFoundLength}: ${element.attributes?.url}...`);
            const { data } = await destinationStrapi.getElements(
                {
                    url: element.attributes.url
                },
                {
                    pagination: {
                        page: 1,
                        pageSize: 1
                    },
                }
            );
            const elementFound = data[0];
            console.log('elementFound: ', !!elementFound?.attributes?.url, elementFound?.attributes?.url);
            if (!elementFound) {
                const elementCreated = await destinationStrapi.createElement({ id: element.id, ...element.attributes });
                console.log('elementCreated: ', elementCreated);
            }
        } catch (error) {
            console.error(`${element.attributes.name} failed`, error);
        }
    }
}

transportElements(
    {
        url: constants.source.url,
        token: constants.source.token,
    },
    {
        url: constants.destination.url,
        token: constants.destination.token,
    }
);
