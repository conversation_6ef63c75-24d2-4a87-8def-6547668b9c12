const oldStrapiFAQS = require('./grouped_faqs.json');

const faqs = {}

const processElements = (elementsToConvert = [], lastId = 0, parentId) => {
    const newFAQIds = []
    elementsToConvert.forEach((faq, index) => {
        const newId = parseInt(`${index}${lastId}`) + 1;
        const newFAQ = refactorMainSchema(faq, newId);
        newFAQIds.push(newId);
        const localizationIds = processElements(faq.locations, newId, newId);
        newFAQ.localizations = [...localizationIds];
        if (parentId) {
            newFAQ.localizations.push(parentId);
        }
        faqs[newId] = newFAQ;
        localizationIds.forEach((id) => {
            faqs[id].localizations = [newId, ...localizationIds.filter((i) => i !== id)]
        })
    })
    return newFAQIds
}

const refactorMainSchema = (input, id) => {
    const output = {
        "id": id,
        "title": input.title,
        "description": `<p>${input.description}</p>`,
        "tags": "",
        "createdAt": new Date().toISOString(),
        "updatedAt": new Date().toISOString(),
        "publishedAt": new Date().toISOString(),
        "locale": input.lang || 'en',
        "sitemap_exclude": false,
        "categories": [],
        'countries': [],
        "localizations": [],
        "createdBy": null,
        "updatedBy": null
    };
    return output;
};

processElements(oldStrapiFAQS);
console.log(JSON.stringify(faqs, null, 2));