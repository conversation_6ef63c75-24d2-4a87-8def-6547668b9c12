const fs = require('fs');
const xml2js = require('xml2js');

// Read the XML file
fs.readFile('./blogs.xml', 'utf8', (err, data) => {
    if (err) {
        console.error('Error reading the file:', err);
        return;
    }

    // Parse the XML content
    xml2js.parseString(data, { explicitArray: false }, (err, result) => {
        if (err) {
            console.error('Error parsing the XML:', err);
            return;
        }

        // Function to group blog entries by sf:Id
        const groupBySfId = (obj) => {
            const blogsById = {};

            const extractBlogs = (obj) => {
                console.log('obj:', obj);
                if (obj["cmis:propertyId"] && obj["cmis:propertyId"].$.propertyDefinitionId === "sf:Id") {
                    const id = obj["cmis:propertyId"]["cmis:value"];
                    if (!blogsById[id]) {
                        blogsById[id] = [];
                    }
                    blogsById[id].push(obj);
                } else if (typeof obj === 'object') {
                    for (const key in obj) {
                        extractBlogs(obj[key]);
                    }
                }
            };

            extractBlogs(obj);
            return blogsById;
        };

        const blogsById = groupBySfId(result);

        // Output the grouped blog entries
        console.log(JSON.stringify(blogsById, null, 2));
        fs.writeFileSync(`blogsBy-${Date.now()}.json`, JSON.stringify(blogsById, null, 2));
    });
});
