import xml.etree.ElementTree as ET
import json

# Define the namespaces
namespaces = {
    'atom': 'http://www.w3.org/2005/Atom',
    'cmis': 'http://docs.oasis-open.org/ns/cmis/core/200908/',
    'cmisra': 'http://docs.oasis-open.org/ns/cmis/restatom/200908/',
    'sf': 'urn:telerik:sitefinity:cmis'
}

# Parse the XML file
tree = ET.parse('blog_sitefinity.xml')
root = tree.getroot()

# Function to extract properties from each cmisra:object
def extract_properties(entry):
    properties = {}
    
    # Extract sf:Title
    title = entry.find(".//cmis:propertyString[@propertyDefinitionId='sf:Title']/cmis:value", namespaces)
    if title is not None:
        properties['title'] = title.text

    # Extract sf:description
    description = entry.find(".//cmis:propertyString[@propertyDefinitionId='sf:description']/cmis:value", namespaces)
    if description is not None:
        properties['description'] = description.text

    # Extract sf:UrlName
    url_name = entry.find(".//cmis:propertyString[@propertyDefinitionId='sf:UrlName']/cmis:value", namespaces)
    if url_name is not None:
        properties['url_name'] = url_name.text

    # Extract sf:MetaDescription
    meta_description = entry.find(".//cmis:propertyString[@propertyDefinitionId='sf:MetaDescription']/cmis:value", namespaces)
    if meta_description is not None:
        properties['meta_description'] = meta_description.text

    # Extract sf:MetaTitle
    meta_title = entry.find(".//cmis:propertyString[@propertyDefinitionId='sf:MetaTitle']/cmis:value", namespaces)
    if meta_title is not None:
        properties['meta_title'] = meta_title.text

    # Extract sf:LangId
    lang_id = entry.find(".//cmis:propertyString[@propertyDefinitionId='sf:LangId']/cmis:value", namespaces)
    if lang_id is not None:
        properties['lang_id'] = lang_id.text

    # Extract sf:Summary
    summary = entry.find(".//cmis:propertyString[@propertyDefinitionId='sf:Summary']/cmis:value", namespaces)
    if summary is not None:
        properties['summary'] = summary.text

    # Extract sf:Content
    content = entry.find(".//cmis:propertyHtml[@propertyDefinitionId='sf:Content']/cmis:value", namespaces)
    if content is not None:
        # Attempt to parse the content as JSON
        try:
            properties['content'] = json.loads(content.text)
        except json.JSONDecodeError:
            # If parsing fails, store as plain text
            properties['content'] = content.text.strip()
    return properties

# List to store the extracted objects
extracted_data = []

# Loop over each cmisra:object and extract properties
for entry in root.findall(".//cmisra:object", namespaces):
    extracted_properties = extract_properties(entry)
    if extracted_properties:
        extracted_data.append(extracted_properties)

# Save the extracted data to a JSON file
with open('extracted_blogs.json', 'w', encoding='utf-8') as json_file:
    json.dump(extracted_data, json_file, indent=4, ensure_ascii=False)

print("Data extraction completed and saved to 'extracted_blogs.json'")
