const fs = require('fs');
const xml2js = require('xml2js');
const xmlFilePath = './blogs_edited_lise.xml'; 

// Read the XML file
fs.readFile(xmlFilePath, 'utf8', (err, data) => {
    if (err) {
        console.error('Error reading the file:', err);
        return;
    }

    // Parse the XML content
    xml2js.parseString(data, { explicitArray: false }, (err, result) => {
        if (err) {
            console.error('Error parsing the XML:', err);
            return;
        }

        // Convert the JSON object to a formatted JSON string
        const json = JSON.stringify(result, null, 2);
        console.log(json);
    });
});