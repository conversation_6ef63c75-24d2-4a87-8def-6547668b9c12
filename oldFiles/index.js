const fs = require('fs');
const axios = require('axios').default;
const newsBlogs = require('./news-blogs.json');

const endpoint = process.env.endpoint;
const authToken = process.env.authToken;
const blogPath = 'api/news-posts?filters[attribue.title][$contains]=hueso';

const saveFailed = (id, data) => {
    const failed = require('./failed.json') || {};
    failed[id] = data;
    fs.writeFileSync(`failed.json`, JSON.stringify(failed, null, 2));
}

const validateData = (data) => {
    if (data.seo && data.seo.metaTitle) {
        data.seo.metaTitle = data.seo.metaTitle.substring(0, 60);
    } else {
        data.seo.metaTitle = data.title.substring(0, 60);
    }
    if (data.seo && data.seo.metaDescription) {
        data.seo.metaDescription = data.seo.metaDescription.substring(0, 120);
    } else {
        data.seo.metaDescription = data.summary.substring(0, 120);
    }
    if (data.publishedAt) {
        data.publishedAt = '2024-10-14T05:05:34.302Z';
    }

    return data;
}

const getByTitle = async (title) => {
    try {
        const headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
        }
        const response = await axios.get(`${endpoint}?filters[title][$containsi]=${title}`, {
            headers
        });
        console.log('Element found:', response.data[0]);
        return response.data.data;
    } catch (error) {
        console.error('Error reading by title:', title, error.response ? error.response.data : error.message);
    }
}

// Function to send data to Strapi
const sendDataToStrapi = async ({ id, data }) => {
    data = validateData(data);
    try {
        const headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
        }
        let response = {};
        if (id) {
            response = await axios.put(`${endpoint}/${id}`, { data }, {
                headers
            });
        } else {
            response = await axios.post(endpoint, { data }, {
                headers
            });
        }
        console.log('Data sent successfully:', response.data.data.id);
        return response.data.data;
    } catch (error) {
        console.log('failed data: ', JSON.stringify(data, null, 2));
        console.error('Error sending data to Strapi:', error.response ? error.response.data : error.message);
    }
};

const putLocalization = async ({ relatedId, newLocaleVersion }) => {
    newLocaleVersion = validateData(newLocaleVersion);
    try {
        const headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
        }
        let response = await axios.post(`${endpoint}/${relatedId}/localizations`, newLocaleVersion, {
            headers
        });
        console.log('new localized version was sent successfully:', response.data.id);
        return response.data;
    } catch (error) {
        console.error('Error sending data to Strapi:', error.response ? error.response.data : error.message);
    }
}

(async () => {
    console.log('Starting process...', endpoint);
    const newElements = require('./newElements.json') || {};
    const failed = {};
    for (const blog of Object.values(newsBlogs).filter((blog) => blog.locale === 'es')) {
        try {
            const { category, id, urls, headerImage, localizations, createdAt, updatedAt, ...data } = blog;
            const blogFound = newElements[id]; // await getByTitle(data.title);
            if (!blogFound) {
                const element = await sendDataToStrapi({ data });
                if (!element) {
                    console.log('Element not found:', data.title);
                    saveFailed(id, data);
                    continue;
                }

                const newLocales = []
                for (const localizationId of localizations) {
                    try {
                        const localizedElement = newsBlogs[localizationId];
                        const { category, id: localizedId, urls, headerImage, localizations, createdAt, updatedAt, ...data } = localizedElement;
                        const localizedBlogFound = newElements[localizedId]; // await getByTitle(data.title);
                        if (!localizedBlogFound) {
                            const newLocale = await putLocalization({ relatedId: element.id, newLocaleVersion: data });
                            newElements[localizedId] = newLocale;
                            newLocales.push(newLocale);
                        }
                    } catch (error) {
                        console.error('Error sending data to Strapi:', error);
                    }
                }

                newElements[id] = {
                    id: element.id,
                    ...element.attributes,
                    localizations: newLocales,
                };
                fs.writeFileSync(`newElements.json`, JSON.stringify(newElements, null, 2));
            }
        } catch (error) {
            console.error('Error sending data to Strapi:', error);
            failed[blog.id] = {
                ...blog,
                error,
            };
        }

    };
})()