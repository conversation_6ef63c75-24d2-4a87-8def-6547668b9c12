const fs = require('fs');
const jsonXML = require('./blogs.json');
const axios = require('axios');

let blogs = {}
const localizations = {}
const idGroups = {}
const parentGroup = {}
const failed = {}

const wait = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// Function to compare content using OpenAI API
const compareContent = async ({ id1, content1, id2, content2 }) => {
    if (!content1 || !content2) {
        return false;
    }
    const apiKey = '***************************************************'; // Replace with your OpenAI API key
    const url = 'https://api.openai.com/v1/chat/completions';
    const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${apiKey}`,
    };

    const body = {
        messages: [
            {
                "role": "system",
                "content": "You are a helpful and professional translator."
            },
        ],
        model: "gpt-3.5-turbo",
        // max_tokens: 1000,
        // temperature: 0.1
    };

    const userMessage = {
        "role": "user",
        "content": "",
    };

    userMessage.content = `Do these two contents mean exactly the same but in different languages?\n\n Content 1: ${content1}\n\n Content 2: ${content2}\n\n  Answer with "yes" or "no".`;
    body.messages.push(userMessage);

    try {
        const response = await axios.post(url, body, { headers });
        const answer = response.data.choices[0].message.content.toLowerCase();
        console.log('answer:', answer)
        const areSimilar = answer.includes('yes');
        if (areSimilar) {
            console.log('Content comparison successful:')
            console.log('content1: ', content1);
            console.log('content2:', content2);
        }
        return areSimilar;
    } catch (error) {
        failed[id1] = failed[id1] ? [...failed[id1], id2] : [id2];
        console.error('Error comparing content:', error);
        return false; // Fallback to false if comparison fails
    }
};

// Read the JSON file
const processElements = async (elementsToConvert = [], lastId = 0, parentId) => {
    let mainIndex = 0;
    for (const blog of elementsToConvert) {
        const newId = parseInt(`${mainIndex}${lastId}`) + 1;
        const newBlog = refactorMainSchema(blog, newId);
        idGroups[newBlog.groupId] = idGroups[newBlog.groupId] ? [...idGroups[newBlog.groupId], newBlog] : [newBlog];
        mainIndex++;
    }

    let blogs = {};
    Object.values(idGroups).forEach((elements, index) => {
        const ids = elements.map(({ id }) => id);
        elements.forEach((element) => {
            blogs[element.id] = {
                ...element,
                localizations: ids.filter((id) => id !== element.id)
            }
        })
    })

    fs.writeFileSync(`news-blogs.json`, JSON.stringify(blogs, null, 2));
};

const getLocations = async () => {
    const existingLaguages = {};
    for (const blog of elementsToConvert) {
        const locale = blog["cmis:properties"]["cmis:propertyString"].find(prop => prop.$.propertyDefinitionId === "sf:LangId")?.["cmis:value"];
        if (locale) {
            existingLaguages[locale] = true
        }
    }
    const maxLanguages = Object.keys(existingLaguages);
    console.log('maxLanguages:', maxLanguages, maxLanguages.length);

    const totalElements = elementsToConvert.length;
    if (!localizations[newId]) {
        localizations[newId] = [];
        let secondaryIndex = 0;
        for (const secondaryBlog of elementsToConvert) {
            if (secondaryIndex > mainIndex && !elementsToConvert[secondaryIndex].done) {
                const secondaryNewId = parseInt(`${mainIndex}${secondaryIndex}`) + 1;
                const secondaryNewBlog = refactorMainSchema(secondaryBlog, secondaryNewId);
                elementsToConvert[secondaryIndex].done = true;

                if (
                    secondaryNewBlog.locale !== newBlog.locale &&
                    !localizations[secondaryNewId]
                ) {
                    const sameContent = await compareContent({
                        id1: newId,
                        content1: newBlog.title,
                        id2: secondaryNewId,
                        content2: secondaryNewBlog.title
                    });
                    if (sameContent) {
                        localizations[secondaryNewId] = [newId];
                        localizations[newId].push(secondaryNewId);
                        secondaryNewBlog.localizations = localizations[newId];
                        blogs[secondaryNewId] = secondaryNewBlog;
                    }
                    console.log('new secondary blog finished...', totalElements, secondaryIndex);
                    wait(50);
                }
            }

            if (localizations[newId].length === maxLanguages.length) {
                break;
            }
            secondaryIndex++;
        }
    }

    console.log('localizations: ', localizations);
    Object.keys(blogs).forEach((key) => {
        blogs[key].localizations = [parseInt(newId), ...localizations[newId]].filter((id) => id !== parseInt(key));
    })

    fs.writeFileSync(`blogsResult-${newId}.json`, JSON.stringify(blogs, null, 2));
    console.log('new complete blog finished...', totalElements, mainIndex);

    blogs = {};
}

const refactorMainSchema = (blog, id) => {
    const output = {
        "groupId": blog["cmis:properties"]["cmis:propertyId"].find(prop => prop.$.propertyDefinitionId === "sf:Id")?.["cmis:value"],
        "id": id,
        "title": blog["cmis:properties"]["cmis:propertyString"].find(prop => prop.$.propertyDefinitionId === "sf:Title")?.["cmis:value"]._,
        "summary": blog["cmis:properties"]["cmis:propertyString"].find(prop => prop.$.propertyDefinitionId === "sf:Summary")?.["cmis:value"]._,
        "tags": blog["cmis:properties"]["cmis:propertyString"].find(prop => prop.$.propertyDefinitionId === "sf:Tags")?.["cmis:value"],
        "content": blog["cmis:properties"]["cmis:propertyString"].find(prop => prop.$.propertyDefinitionId === "sf:Content")?.["cmis:value"]._,
        "createdAt": new Date().toISOString(),
        "updatedAt": new Date().toISOString(),
        "publishedAt": blog["cmis:properties"]["cmis:propertyString"].find(prop => prop.$.propertyDefinitionId === "sf:ApprovalWorkflowState")?.["cmis:value"]._,
        "locale": blog["cmis:properties"]["cmis:propertyString"].find(prop => prop.$.propertyDefinitionId === "sf:LangId")?.["cmis:value"],
        "sitemap_exclude": blog["cmis:properties"]["cmis:propertyBoolean"].find(prop => prop.$.propertyDefinitionId === "sf:IncludeInSitemap")?.["cmis:value"] === "False",
        "category": blog["cmis:properties"]["cmis:propertyId"].find(prop => prop.$.propertyDefinitionId === "sf:Category")?.["cmis:value"],
        "headerImage": blog["cmis:properties"]["sf:Telerik.Sitefinity.RelatedData.RelatedItems"]?.["cmis:value"],
        "sourceInfo": null,
        "postSettings": null,
        "urls": "", //blog["cmis:properties"]["sf:Telerik.Sitefinity.Blogs.Model.BlogPostUrlData"]?.["cmis:value"],
        "seo": {
            "metaDescription": blog["cmis:properties"]["cmis:propertyString"].find(prop => prop.$.propertyDefinitionId === "sf:MetaDescription")?.["cmis:value"]._,
            "metaTitle": blog["cmis:properties"]["cmis:propertyString"].find(prop => prop.$.propertyDefinitionId === "sf:MetaTitle")?.["cmis:value"]._
        },
        "localizations": [],
        "createdBy": null,
        "updatedBy": null
    };
    return output;
};

(async () => {
    await processElements(jsonXML["atom:entry"]["cmisra:object"]);
})()
