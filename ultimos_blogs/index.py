import json
import xml.etree.ElementTree as ET
import re

# Configuración de autenticación
SITEFINITY_URL = "https://www.sendvalu.com"
IMAGES_ENDPOINT = f"{SITEFINITY_URL}/Sitefinity/adminapp/content/images"

# Función que procesa el atributo src de las imágenes
def process_image_src(content, parent_image_id):
    # Expresión regular para encontrar las imágenes dentro de src
    img_pattern = re.compile(r'src="\[images%7COpenAccessDataProvider\](.*?)\?')
    
    # Función para reemplazar cada src encontrado con la URL deseada
    def replace_src(match):
        # Extraemos el image_id del src
        image_id = match.group(1)
        # Formateamos la nueva URL
        new_src = f"{IMAGES_ENDPOINT}/{parent_image_id}/images/{image_id}/edit?sf_provider=OpenAccessDataProvider&sf_culture=en"
        return f'src="{new_src}"'
    
    # Usamos la expresión regular para reemplazar todos los src en el contenido
    content = img_pattern.sub(replace_src, content)
    
    return content
    
def parse_blog_entries(xml_file, session):
    tree = ET.parse(xml_file)
    root = tree.getroot()
    namespaces = {
        'cmis': 'http://docs.oasis-open.org/ns/cmis/core/200908/',
        'cmisra': 'http://docs.oasis-open.org/ns/cmis/restatom/200908/',
        'sf': 'urn:telerik:sitefinity:cmis'
    }
    
    blog_entries = {}
    
    # Generamos un número de ID por cada entrada
    entry_id = 1
    
    for obj in root.findall('.//cmisra:object', namespaces):
        entry = {}
        
        # Rellenar los valores con la estructura deseada
        for prop in obj.findall('.//cmis:properties/*', namespaces):
            key = prop.attrib.get('propertyDefinitionId', 'unknown')
            value = prop.find('./cmis:value', namespaces)
            entry[key] = value.text if value is not None else None
        
        # Crear la estructura solicitada
        formatted_entry = {        
                "id": entry_id,
                "parentId":entry.get('sf:Id',''),
                "title": entry.get('sf:Title', ''),
                "summary": entry.get('sf:Summary', ''),
                "content": process_image_src(entry.get('sf:Content', ''), entry.get('sf:OriginalContentId','')),
                "publishedAt": "Published",  # Valor estático según tu solicitud
                "locale": entry.get('sf:LangId', ''),
                "includeInsitemap": entry.get('sf:IncludeInSitemap', ''),
                "urls": entry.get('sf:UrlName', ''),
                "seo": {
                    "metaDescription": entry.get('sf:MetaDescription', ''),
                    "metaTitle": entry.get('sf:MetaTitle', '')
                },
                "headerImage": entry.get('sf:HeadImage', ''),
                "headerImageUrls": []
            }
        
        
        # Procesar imágenes si existen
        if "sf:HeadImage" in entry and entry["sf:HeadImage"]:
            try:
                head_image_data = json.loads(entry["sf:HeadImage"])
                image_urls = []
                for img in head_image_data:
                    image_id = img['ChildItemId']
                    parent_image_id = img['ParentItemId']
                    image_url = f"{IMAGES_ENDPOINT}/{parent_image_id}/images/{image_id}/edit?sf_provider=OpenAccessDataProvider&sf_culture=en"
                    image_urls.append(image_url)
                
                formatted_entry["headerImageUrls"] = image_urls
            except json.JSONDecodeError:
                formatted_entry["headerImageUrls"] = []
        
        # Añadir la entrada procesada a la lista
        blog_entries[entry_id]= formatted_entry
        
        # Incrementar el ID para la siguiente entrada
        entry_id += 1
    
    return blog_entries

# Ruta del archivo XML de entrada
xml_file = "blogs.xml"

# Parsear el XML y extraer los datos
blogs_data = parse_blog_entries(xml_file, None)

# Guardar en un archivo JSON
output_file = "blog.json"
with open(output_file, "w", encoding="utf-8") as f:
    json.dump(blogs_data, f, indent=4, ensure_ascii=False)

print(f"JSON generado en {output_file}")
