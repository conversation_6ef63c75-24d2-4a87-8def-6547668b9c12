version: '3.8'
services:
  n8n:
    image: n8nio/n8n
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin # CHANGE THIS!
      - N8N_BASIC_AUTH_PASSWORD=admin # CHANGE THIS!
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=db
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_USER=n8n
      - DB_POSTGRESDB_PASSWORD=n8n_db_password # CHANGE THIS!
      - NODE_FUNCTION_ALLOW_EXTERNAL=axios,qs # Optional, useful for custom API calls
      - GENERIC_TIMEZONE=America/Havana # Example, set your timezone
      - N8N_SECURE_COOKIE=false # Set to true if using HTTPS, false for local IP access
      - NODE_ENV=production # Optimizes n8n for stable performance
    depends_on:
      - db
    volumes:
      - n8n_data:/home/<USER>/.n8n # Persistent storage for n8n data
    restart: unless-stopped
  db:
    image: postgres:12 # Using PostgreSQL version 12
    volumes:
      - db-data:/var/lib/postgresql/data # Persistent storage for PostgreSQL data
    environment:
      - POSTGRES_USER=n8n
      - POSTGRES_PASSWORD=n8n_db_password # CHANGE THIS!
      - POSTGRES_DB=n8n
    restart: unless-stopped
  mcp-server:
    build:
      context: ./mcp-server
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - MCP_SERVER_PORT=3001
      - LOG_LEVEL=info
      - STRAPI_DEV_URL=${STRAPI_DEV_URL}
      - STRAPI_DEV_TOKEN=${STRAPI_DEV_TOKEN}
      - STRAPI_PROD_URL=${STRAPI_PROD_URL}
      - STRAPI_PROD_TOKEN=${STRAPI_PROD_TOKEN}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - RATE_LIMIT_ENABLED=true
      - RATE_LIMIT_MAX_REQUESTS=100
      - RATE_LIMIT_WINDOW_MS=900000
    volumes:
      - mcp_logs:/app/logs
    restart: unless-stopped
    depends_on:
      - n8n

volumes:
  n8n_data:
  db-data:
  mcp_logs: