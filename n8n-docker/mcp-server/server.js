import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { WebSocketServer } from 'ws';
import { createServer } from 'http';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import { RateLimiterMemory } from 'rate-limiter-flexible';
import winston from 'winston';

import { MCPProtocolHandler } from './src/protocol-handler.js';
import { StrapiConnector } from './src/strapi-connector.js';
import { AuthMiddleware } from './src/auth-middleware.js';
import { ValidationMiddleware } from './src/validation-middleware.js';

// Load environment variables
dotenv.config();

// Configure logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' })
  ]
});

// Rate limiter configuration
const rateLimiter = new RateLimiterMemory({
  keyGenerator: (req) => req.ip,
  points: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  duration: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900, // 15 minutes
});

class MCPServer {
  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.wss = new WebSocketServer({ server: this.server });
    this.port = process.env.MCP_SERVER_PORT || 3001;
    this.clients = new Map();
    
    // Initialize components
    this.protocolHandler = new MCPProtocolHandler(logger);
    this.strapiConnector = new StrapiConnector(logger);
    this.authMiddleware = new AuthMiddleware(logger);
    this.validationMiddleware = new ValidationMiddleware(logger);
    
    this.setupMiddleware();
    this.setupRoutes();
    this.setupWebSocket();
    this.setupErrorHandling();
  }

  setupMiddleware() {
    // Security middleware
    this.app.use(helmet());
    this.app.use(cors({
      origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:5678'],
      credentials: true
    }));

    // Rate limiting
    if (process.env.RATE_LIMIT_ENABLED === 'true') {
      this.app.use(async (req, res, next) => {
        try {
          await rateLimiter.consume(req.ip);
          next();
        } catch (rejRes) {
          res.status(429).json({
            error: 'Too Many Requests',
            retryAfter: Math.round(rejRes.msBeforeNext / 1000)
          });
        }
      });
    }

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));

    // Request logging
    this.app.use((req, res, next) => {
      logger.info(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        requestId: uuidv4()
      });
      next();
    });
  }

  setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        uptime: process.uptime()
      });
    });

    // Authentication endpoint
    this.app.post('/auth/token', this.validationMiddleware.validateAuth, async (req, res) => {
      try {
        const { apiKey, environment } = req.body;
        const token = await this.authMiddleware.generateToken(apiKey, environment);
        
        res.json({
          token,
          expiresIn: '24h',
          environment
        });
      } catch (error) {
        logger.error('Authentication failed', { error: error.message });
        res.status(401).json({ error: 'Authentication failed' });
      }
    });

    // MCP Protocol endpoints
    this.app.post('/mcp/initialize', 
      this.authMiddleware.authenticate,
      this.validationMiddleware.validateInitialize,
      async (req, res) => {
        try {
          const result = await this.protocolHandler.initialize(req.body, req.user);
          res.json(result);
        } catch (error) {
          logger.error('MCP initialization failed', { error: error.message });
          res.status(400).json({ error: error.message });
        }
      }
    );

    this.app.post('/mcp/tools/list',
      this.authMiddleware.authenticate,
      async (req, res) => {
        try {
          const tools = await this.protocolHandler.listTools(req.user);
          res.json({ tools });
        } catch (error) {
          logger.error('Failed to list tools', { error: error.message });
          res.status(500).json({ error: error.message });
        }
      }
    );

    this.app.post('/mcp/tools/call',
      this.authMiddleware.authenticate,
      this.validationMiddleware.validateToolCall,
      async (req, res) => {
        try {
          const result = await this.protocolHandler.callTool(req.body, req.user);
          res.json(result);
        } catch (error) {
          logger.error('Tool call failed', { error: error.message });
          res.status(400).json({ error: error.message });
        }
      }
    );

    // Strapi proxy endpoints
    this.app.use('/strapi', 
      this.authMiddleware.authenticate,
      this.strapiConnector.createProxy()
    );

    // Error handling for undefined routes
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Endpoint not found',
        path: req.originalUrl,
        method: req.method
      });
    });
  }

  setupWebSocket() {
    this.wss.on('connection', (ws, req) => {
      const clientId = uuidv4();
      logger.info(`WebSocket client connected: ${clientId}`);

      // Store client connection
      this.clients.set(clientId, {
        ws,
        authenticated: false,
        user: null,
        connectedAt: new Date()
      });

      ws.on('message', async (data) => {
        try {
          const message = JSON.parse(data.toString());
          await this.handleWebSocketMessage(clientId, message);
        } catch (error) {
          logger.error('WebSocket message error', { 
            clientId, 
            error: error.message 
          });
          ws.send(JSON.stringify({
            type: 'error',
            error: 'Invalid message format'
          }));
        }
      });

      ws.on('close', () => {
        logger.info(`WebSocket client disconnected: ${clientId}`);
        this.clients.delete(clientId);
      });

      ws.on('error', (error) => {
        logger.error('WebSocket error', { clientId, error: error.message });
        this.clients.delete(clientId);
      });

      // Send welcome message
      ws.send(JSON.stringify({
        type: 'welcome',
        clientId,
        timestamp: new Date().toISOString()
      }));
    });
  }

  async handleWebSocketMessage(clientId, message) {
    const client = this.clients.get(clientId);
    if (!client) return;

    const { ws } = client;

    switch (message.type) {
      case 'authenticate':
        try {
          const user = await this.authMiddleware.verifyToken(message.token);
          client.authenticated = true;
          client.user = user;
          
          ws.send(JSON.stringify({
            type: 'authenticated',
            user: { environment: user.environment }
          }));
        } catch (error) {
          ws.send(JSON.stringify({
            type: 'auth_error',
            error: 'Authentication failed'
          }));
        }
        break;

      case 'subscribe':
        if (!client.authenticated) {
          ws.send(JSON.stringify({
            type: 'error',
            error: 'Authentication required'
          }));
          return;
        }
        
        // Handle subscription to Strapi events
        await this.handleSubscription(clientId, message.subscription);
        break;

      case 'tool_call':
        if (!client.authenticated) {
          ws.send(JSON.stringify({
            type: 'error',
            error: 'Authentication required'
          }));
          return;
        }

        try {
          const result = await this.protocolHandler.callTool(message, client.user);
          ws.send(JSON.stringify({
            type: 'tool_result',
            requestId: message.requestId,
            result
          }));
        } catch (error) {
          ws.send(JSON.stringify({
            type: 'tool_error',
            requestId: message.requestId,
            error: error.message
          }));
        }
        break;

      default:
        ws.send(JSON.stringify({
          type: 'error',
          error: `Unknown message type: ${message.type}`
        }));
    }
  }

  async handleSubscription(clientId, subscription) {
    const client = this.clients.get(clientId);
    if (!client) return;

    // Implement subscription logic for real-time Strapi updates
    logger.info(`Client ${clientId} subscribed to: ${subscription.type}`);
    
    client.ws.send(JSON.stringify({
      type: 'subscription_confirmed',
      subscription
    }));
  }

  setupErrorHandling() {
    // Global error handler
    this.app.use((error, req, res, next) => {
      logger.error('Unhandled error', {
        error: error.message,
        stack: error.stack,
        path: req.path,
        method: req.method
      });

      res.status(500).json({
        error: 'Internal server error',
        requestId: req.requestId
      });
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception', { error: error.message, stack: error.stack });
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled rejection', { reason, promise });
    });
  }

  start() {
    this.server.listen(this.port, () => {
      logger.info(`MCP Server running on port ${this.port}`);
      logger.info(`WebSocket server ready for connections`);
      logger.info(`Health check available at http://localhost:${this.port}/health`);
    });
  }

  stop() {
    this.wss.close();
    this.server.close();
    logger.info('MCP Server stopped');
  }
}

// Start server if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const server = new MCPServer();
  server.start();

  // Graceful shutdown
  process.on('SIGTERM', () => {
    logger.info('SIGTERM received, shutting down gracefully');
    server.stop();
    process.exit(0);
  });

  process.on('SIGINT', () => {
    logger.info('SIGINT received, shutting down gracefully');
    server.stop();
    process.exit(0);
  });
}

export { MCPServer };
