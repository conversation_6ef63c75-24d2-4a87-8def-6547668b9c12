{"name": "strapi-mcp-server", "version": "1.0.0", "description": "Model Context Protocol server for Strapi CMS integration", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "node --watch server.js", "test": "node test/test-server.js"}, "keywords": ["mcp", "model-context-protocol", "strapi", "cms", "api"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.4.5", "axios": "^1.7.7", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "rate-limiter-flexible": "^5.0.3", "winston": "^3.11.0", "ws": "^8.16.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}