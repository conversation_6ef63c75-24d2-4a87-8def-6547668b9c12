import jwt from 'jsonwebtoken';
import crypto from 'crypto';

/**
 * Authentication Middleware for MCP Server
 */
export class AuthMiddleware {
  constructor(logger) {
    this.logger = logger;
    this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    this.validApiKeys = new Map([
      ['dev-api-key', { environment: 'development', permissions: ['read', 'write'] }],
      ['prod-api-key', { environment: 'production', permissions: ['read', 'write'] }]
    ]);
  }

  /**
   * Generate JWT token for authenticated user
   */
  async generateToken(apiKey, environment) {
    // Validate API key
    const keyData = this.validApiKeys.get(apiKey);
    if (!keyData) {
      throw new Error('Invalid API key');
    }

    // Validate environment matches
    if (environment && keyData.environment !== environment) {
      throw new Error('Environment mismatch');
    }

    const payload = {
      environment: keyData.environment,
      permissions: keyData.permissions,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
    };

    const token = jwt.sign(payload, this.jwtSecret);
    
    this.logger.info('Token generated', { 
      environment: keyData.environment,
      permissions: keyData.permissions 
    });

    return token;
  }

  /**
   * Verify JWT token
   */
  async verifyToken(token) {
    try {
      const decoded = jwt.verify(token, this.jwtSecret);
      return decoded;
    } catch (error) {
      throw new Error('Invalid or expired token');
    }
  }

  /**
   * Express middleware for authentication
   */
  authenticate = async (req, res, next) => {
    try {
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ error: 'Missing or invalid authorization header' });
      }

      const token = authHeader.substring(7);
      const user = await this.verifyToken(token);
      
      req.user = user;
      next();
    } catch (error) {
      this.logger.warn('Authentication failed', { error: error.message });
      res.status(401).json({ error: 'Authentication failed' });
    }
  };

  /**
   * Check if user has required permission
   */
  hasPermission(user, permission) {
    return user.permissions && user.permissions.includes(permission);
  }

  /**
   * Express middleware for permission checking
   */
  requirePermission = (permission) => {
    return (req, res, next) => {
      if (!this.hasPermission(req.user, permission)) {
        return res.status(403).json({ 
          error: `Insufficient permissions. Required: ${permission}` 
        });
      }
      next();
    };
  };

  /**
   * Generate secure API key
   */
  generateApiKey() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Add new API key (for dynamic key management)
   */
  addApiKey(apiKey, environment, permissions = ['read']) {
    this.validApiKeys.set(apiKey, { environment, permissions });
    this.logger.info('API key added', { environment, permissions });
  }

  /**
   * Remove API key
   */
  removeApiKey(apiKey) {
    const removed = this.validApiKeys.delete(apiKey);
    if (removed) {
      this.logger.info('API key removed');
    }
    return removed;
  }

  /**
   * List all API keys (without exposing the actual keys)
   */
  listApiKeys() {
    const keys = [];
    for (const [key, data] of this.validApiKeys.entries()) {
      keys.push({
        keyHash: crypto.createHash('sha256').update(key).digest('hex').substring(0, 8),
        environment: data.environment,
        permissions: data.permissions
      });
    }
    return keys;
  }
}
