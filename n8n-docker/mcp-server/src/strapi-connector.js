import axios from 'axios';
import { createProxyMiddleware } from 'http-proxy-middleware';

/**
 * Strapi Connector for MCP Server
 * Handles direct communication with Strapi instances
 */
export class StrapiConnector {
  constructor(logger) {
    this.logger = logger;
    this.connections = new Map();
    this.initializeConnections();
  }

  /**
   * Initialize Strapi connections for different environments
   */
  initializeConnections() {
    const environments = ['development', 'production'];
    
    environments.forEach(env => {
      const url = env === 'production' 
        ? process.env.STRAPI_PROD_URL 
        : process.env.STRAPI_DEV_URL;
      
      const token = env === 'production'
        ? process.env.STRAPI_PROD_TOKEN
        : process.env.STRAPI_DEV_TOKEN;

      if (url && token) {
        const client = axios.create({
          baseURL: `${url}/api`,
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        });

        // Add request interceptor for logging
        client.interceptors.request.use(
          (config) => {
            this.logger.debug('Strapi API request', {
              environment: env,
              method: config.method?.toUpperCase(),
              url: config.url,
              params: config.params
            });
            return config;
          },
          (error) => {
            this.logger.error('Strapi API request error', { error: error.message });
            return Promise.reject(error);
          }
        );

        // Add response interceptor for logging
        client.interceptors.response.use(
          (response) => {
            this.logger.debug('Strapi API response', {
              environment: env,
              status: response.status,
              url: response.config.url
            });
            return response;
          },
          (error) => {
            this.logger.error('Strapi API response error', {
              environment: env,
              status: error.response?.status,
              message: error.message,
              url: error.config?.url
            });
            return Promise.reject(error);
          }
        );

        this.connections.set(env, {
          client,
          url,
          token,
          lastHealthCheck: null,
          isHealthy: null
        });

        this.logger.info(`Strapi connection initialized`, { environment: env, url });
      } else {
        this.logger.warn(`Strapi connection not configured`, { environment: env });
      }
    });
  }

  /**
   * Get Strapi client for specific environment
   */
  getClient(environment) {
    const connection = this.connections.get(environment);
    if (!connection) {
      throw new Error(`Strapi connection not found for environment: ${environment}`);
    }
    return connection.client;
  }

  /**
   * Health check for Strapi instance
   */
  async healthCheck(environment) {
    try {
      const connection = this.connections.get(environment);
      if (!connection) {
        return { healthy: false, error: 'Connection not configured' };
      }

      const response = await connection.client.get('/users/me');
      const isHealthy = response.status === 200;
      
      connection.lastHealthCheck = new Date();
      connection.isHealthy = isHealthy;

      this.logger.info('Strapi health check', { 
        environment, 
        healthy: isHealthy,
        responseTime: response.headers['x-response-time']
      });

      return { 
        healthy: isHealthy, 
        responseTime: response.headers['x-response-time'],
        timestamp: connection.lastHealthCheck
      };
    } catch (error) {
      this.logger.error('Strapi health check failed', {
        environment,
        error: error.message
      });

      const connection = this.connections.get(environment);
      if (connection) {
        connection.lastHealthCheck = new Date();
        connection.isHealthy = false;
      }

      return { 
        healthy: false, 
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Get all content types from Strapi
   */
  async getContentTypes(environment) {
    try {
      const client = this.getClient(environment);
      
      // Try to get content types from admin API
      try {
        const response = await client.get('/content-manager/content-types');
        return response.data;
      } catch (adminError) {
        // Fallback: try to infer from available endpoints
        this.logger.warn('Admin API not accessible, using fallback method', {
          environment,
          error: adminError.message
        });
        
        // Common content types to check
        const commonTypes = [
          'articles', 'pages', 'posts', 'products', 'categories',
          'users', 'media', 'comments', 'tags', 'events'
        ];
        
        const availableTypes = [];
        
        for (const type of commonTypes) {
          try {
            await client.get(`/${type}?pagination[limit]=1`);
            availableTypes.push(type);
          } catch (error) {
            // Type doesn't exist or not accessible
          }
        }
        
        return availableTypes.map(type => ({ uid: type, apiID: type }));
      }
    } catch (error) {
      this.logger.error('Failed to get content types', {
        environment,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Create proxy middleware for Strapi API
   */
  createProxy() {
    return createProxyMiddleware({
      target: 'http://localhost:1337', // Default, will be overridden
      changeOrigin: true,
      pathRewrite: {
        '^/strapi': '/api'
      },
      router: (req) => {
        // Route to appropriate Strapi instance based on user environment
        const environment = req.user?.environment || 'development';
        const connection = this.connections.get(environment);
        
        if (!connection) {
          throw new Error(`No Strapi connection for environment: ${environment}`);
        }
        
        return connection.url;
      },
      onProxyReq: (proxyReq, req, res) => {
        // Add authentication header
        const environment = req.user?.environment || 'development';
        const connection = this.connections.get(environment);
        
        if (connection) {
          proxyReq.setHeader('Authorization', `Bearer ${connection.token}`);
        }

        this.logger.debug('Proxying request to Strapi', {
          environment,
          method: req.method,
          path: req.path,
          target: proxyReq.getHeader('host')
        });
      },
      onProxyRes: (proxyRes, req, res) => {
        this.logger.debug('Proxy response from Strapi', {
          environment: req.user?.environment,
          status: proxyRes.statusCode,
          path: req.path
        });
      },
      onError: (err, req, res) => {
        this.logger.error('Proxy error', {
          environment: req.user?.environment,
          error: err.message,
          path: req.path
        });
        
        res.status(502).json({
          error: 'Proxy error',
          message: 'Failed to connect to Strapi instance'
        });
      }
    });
  }

  /**
   * Batch operations helper
   */
  async batchOperation(environment, operation, contentType, items, batchSize = 10) {
    const client = this.getClient(environment);
    const results = [];
    const errors = [];

    this.logger.info('Starting batch operation', {
      environment,
      operation,
      contentType,
      totalItems: items.length,
      batchSize
    });

    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      
      this.logger.debug('Processing batch', {
        batchNumber: Math.floor(i / batchSize) + 1,
        batchSize: batch.length
      });

      const batchPromises = batch.map(async (item, index) => {
        try {
          let result;
          const itemIndex = i + index;

          switch (operation) {
            case 'create':
              result = await client.post(`/${contentType}`, { data: item });
              break;
            case 'update':
              if (!item.id) {
                throw new Error('ID required for update operation');
              }
              result = await client.put(`/${contentType}/${item.id}`, { data: item });
              break;
            case 'delete':
              if (!item.id) {
                throw new Error('ID required for delete operation');
              }
              result = await client.delete(`/${contentType}/${item.id}`);
              break;
            default:
              throw new Error(`Unknown operation: ${operation}`);
          }

          return {
            success: true,
            index: itemIndex,
            data: result.data,
            item
          };
        } catch (error) {
          return {
            success: false,
            index: itemIndex,
            error: error.message,
            item
          };
        }
      });

      const batchResults = await Promise.allSettled(batchPromises);
      
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          if (result.value.success) {
            results.push(result.value);
          } else {
            errors.push(result.value);
          }
        } else {
          errors.push({
            success: false,
            index: i + index,
            error: result.reason?.message || 'Unknown error',
            item: batch[index]
          });
        }
      });

      // Add delay between batches to avoid overwhelming the API
      if (i + batchSize < items.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    this.logger.info('Batch operation completed', {
      environment,
      operation,
      contentType,
      totalItems: items.length,
      successful: results.length,
      failed: errors.length
    });

    return {
      operation,
      contentType,
      environment,
      totalItems: items.length,
      successful: results.length,
      failed: errors.length,
      results,
      errors,
      completedAt: new Date().toISOString()
    };
  }

  /**
   * Search content across multiple fields
   */
  async searchContent(environment, contentType, query, options = {}) {
    const client = this.getClient(environment);
    const { fields = [], limit = 25, populate = '*' } = options;

    // Build search filters
    const filters = {};
    
    if (fields.length > 0) {
      filters.$or = fields.map(field => ({
        [field]: { $containsi: query }
      }));
    } else {
      // Default search fields
      filters.$or = [
        { title: { $containsi: query } },
        { name: { $containsi: query } },
        { content: { $containsi: query } },
        { description: { $containsi: query } }
      ];
    }

    try {
      const response = await client.get(`/${contentType}`, {
        params: {
          filters,
          pagination: { limit },
          populate
        }
      });

      this.logger.info('Content search completed', {
        environment,
        contentType,
        query,
        resultsCount: response.data.data?.length || 0
      });

      return response.data;
    } catch (error) {
      this.logger.error('Content search failed', {
        environment,
        contentType,
        query,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get connection status for all environments
   */
  getConnectionStatus() {
    const status = {};
    
    for (const [env, connection] of this.connections.entries()) {
      status[env] = {
        configured: true,
        url: connection.url,
        lastHealthCheck: connection.lastHealthCheck,
        isHealthy: connection.isHealthy
      };
    }

    return status;
  }
}
