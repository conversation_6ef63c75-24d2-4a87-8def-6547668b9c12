import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';

/**
 * MCP Protocol Handler
 * Implements the Model Context Protocol for Strapi CMS integration
 */
export class MCPProtocolHandler {
  constructor(logger) {
    this.logger = logger;
    this.tools = this.initializeTools();
    this.sessions = new Map();
  }

  /**
   * Initialize available tools for MCP
   */
  initializeTools() {
    return {
      'strapi_get_content': {
        name: 'strapi_get_content',
        description: 'Retrieve content from Strapi CMS',
        inputSchema: {
          type: 'object',
          properties: {
            contentType: { type: 'string', description: 'Content type to retrieve' },
            id: { type: 'string', description: 'Optional content ID for specific item' },
            filters: { type: 'object', description: 'Optional filters for content query' },
            populate: { type: 'string', description: 'Fields to populate' },
            locale: { type: 'string', description: 'Content locale' }
          },
          required: ['contentType']
        }
      },
      'strapi_create_content': {
        name: 'strapi_create_content',
        description: 'Create new content in Strapi CMS',
        inputSchema: {
          type: 'object',
          properties: {
            contentType: { type: 'string', description: 'Content type to create' },
            data: { type: 'object', description: 'Content data to create' },
            locale: { type: 'string', description: 'Content locale' }
          },
          required: ['contentType', 'data']
        }
      },
      'strapi_update_content': {
        name: 'strapi_update_content',
        description: 'Update existing content in Strapi CMS',
        inputSchema: {
          type: 'object',
          properties: {
            contentType: { type: 'string', description: 'Content type to update' },
            id: { type: 'string', description: 'Content ID to update' },
            data: { type: 'object', description: 'Updated content data' },
            locale: { type: 'string', description: 'Content locale' }
          },
          required: ['contentType', 'id', 'data']
        }
      },
      'strapi_delete_content': {
        name: 'strapi_delete_content',
        description: 'Delete content from Strapi CMS',
        inputSchema: {
          type: 'object',
          properties: {
            contentType: { type: 'string', description: 'Content type to delete from' },
            id: { type: 'string', description: 'Content ID to delete' }
          },
          required: ['contentType', 'id']
        }
      },
      'strapi_bulk_operation': {
        name: 'strapi_bulk_operation',
        description: 'Perform bulk operations on Strapi content',
        inputSchema: {
          type: 'object',
          properties: {
            operation: { type: 'string', enum: ['create', 'update', 'delete'], description: 'Bulk operation type' },
            contentType: { type: 'string', description: 'Content type for bulk operation' },
            items: { type: 'array', description: 'Array of items for bulk operation' },
            batchSize: { type: 'number', description: 'Batch size for processing', default: 10 }
          },
          required: ['operation', 'contentType', 'items']
        }
      },
      'strapi_search_content': {
        name: 'strapi_search_content',
        description: 'Search content in Strapi CMS',
        inputSchema: {
          type: 'object',
          properties: {
            contentType: { type: 'string', description: 'Content type to search' },
            query: { type: 'string', description: 'Search query' },
            fields: { type: 'array', description: 'Fields to search in' },
            limit: { type: 'number', description: 'Maximum results to return', default: 25 }
          },
          required: ['contentType', 'query']
        }
      },
      'strapi_get_schema': {
        name: 'strapi_get_schema',
        description: 'Get content type schema from Strapi',
        inputSchema: {
          type: 'object',
          properties: {
            contentType: { type: 'string', description: 'Content type to get schema for' }
          },
          required: ['contentType']
        }
      }
    };
  }

  /**
   * Initialize MCP session
   */
  async initialize(params, user) {
    const sessionId = uuidv4();
    
    const session = {
      id: sessionId,
      user,
      capabilities: {
        tools: true,
        resources: true,
        prompts: false
      },
      protocolVersion: '2024-11-05',
      serverInfo: {
        name: 'strapi-mcp-server',
        version: '1.0.0'
      },
      createdAt: new Date().toISOString()
    };

    this.sessions.set(sessionId, session);
    
    this.logger.info('MCP session initialized', { 
      sessionId, 
      user: user.environment 
    });

    return {
      protocolVersion: session.protocolVersion,
      capabilities: session.capabilities,
      serverInfo: session.serverInfo,
      sessionId
    };
  }

  /**
   * List available tools
   */
  async listTools(user) {
    const toolList = Object.values(this.tools);
    
    this.logger.info('Tools listed', { 
      count: toolList.length, 
      user: user.environment 
    });

    return toolList;
  }

  /**
   * Call a specific tool
   */
  async callTool(params, user) {
    const { name, arguments: args } = params;
    
    if (!this.tools[name]) {
      throw new Error(`Unknown tool: ${name}`);
    }

    this.logger.info('Tool called', { 
      tool: name, 
      user: user.environment,
      args: JSON.stringify(args)
    });

    try {
      let result;
      
      switch (name) {
        case 'strapi_get_content':
          result = await this.handleGetContent(args, user);
          break;
        case 'strapi_create_content':
          result = await this.handleCreateContent(args, user);
          break;
        case 'strapi_update_content':
          result = await this.handleUpdateContent(args, user);
          break;
        case 'strapi_delete_content':
          result = await this.handleDeleteContent(args, user);
          break;
        case 'strapi_bulk_operation':
          result = await this.handleBulkOperation(args, user);
          break;
        case 'strapi_search_content':
          result = await this.handleSearchContent(args, user);
          break;
        case 'strapi_get_schema':
          result = await this.handleGetSchema(args, user);
          break;
        default:
          throw new Error(`Tool implementation not found: ${name}`);
      }

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2)
          }
        ],
        isError: false
      };

    } catch (error) {
      this.logger.error('Tool execution failed', {
        tool: name,
        error: error.message,
        user: user.environment
      });

      return {
        content: [
          {
            type: 'text',
            text: `Error: ${error.message}`
          }
        ],
        isError: true
      };
    }
  }

  /**
   * Get Strapi API URL based on user environment
   */
  getStrapiUrl(user) {
    return user.environment === 'production' 
      ? process.env.STRAPI_PROD_URL 
      : process.env.STRAPI_DEV_URL;
  }

  /**
   * Get Strapi API token based on user environment
   */
  getStrapiToken(user) {
    return user.environment === 'production'
      ? process.env.STRAPI_PROD_TOKEN
      : process.env.STRAPI_DEV_TOKEN;
  }

  /**
   * Create Axios instance for Strapi API calls
   */
  createStrapiClient(user) {
    return axios.create({
      baseURL: `${this.getStrapiUrl(user)}/api`,
      headers: {
        'Authorization': `Bearer ${this.getStrapiToken(user)}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });
  }

  /**
   * Handle get content operation
   */
  async handleGetContent(args, user) {
    const { contentType, id, filters, populate, locale } = args;
    const client = this.createStrapiClient(user);

    let url = `/${contentType}`;
    if (id) {
      url += `/${id}`;
    }

    const params = {};
    if (filters) params.filters = filters;
    if (populate) params.populate = populate;
    if (locale) params.locale = locale;

    const response = await client.get(url, { params });
    return response.data;
  }

  /**
   * Handle create content operation
   */
  async handleCreateContent(args, user) {
    const { contentType, data, locale } = args;
    const client = this.createStrapiClient(user);

    const payload = { data };
    if (locale) payload.locale = locale;

    const response = await client.post(`/${contentType}`, payload);
    return response.data;
  }

  /**
   * Handle update content operation
   */
  async handleUpdateContent(args, user) {
    const { contentType, id, data, locale } = args;
    const client = this.createStrapiClient(user);

    const payload = { data };
    if (locale) payload.locale = locale;

    const response = await client.put(`/${contentType}/${id}`, payload);
    return response.data;
  }

  /**
   * Handle delete content operation
   */
  async handleDeleteContent(args, user) {
    const { contentType, id } = args;
    const client = this.createStrapiClient(user);

    const response = await client.delete(`/${contentType}/${id}`);
    return response.data;
  }

  /**
   * Handle bulk operations
   */
  async handleBulkOperation(args, user) {
    const { operation, contentType, items, batchSize = 10 } = args;
    const client = this.createStrapiClient(user);
    const results = [];
    const errors = [];

    // Process items in batches
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchPromises = batch.map(async (item) => {
        try {
          let result;
          switch (operation) {
            case 'create':
              result = await client.post(`/${contentType}`, { data: item });
              break;
            case 'update':
              result = await client.put(`/${contentType}/${item.id}`, { data: item });
              break;
            case 'delete':
              result = await client.delete(`/${contentType}/${item.id}`);
              break;
            default:
              throw new Error(`Unknown bulk operation: ${operation}`);
          }
          return { success: true, data: result.data, item };
        } catch (error) {
          return { success: false, error: error.message, item };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      
      batchResults.forEach(result => {
        if (result.success) {
          results.push(result);
        } else {
          errors.push(result);
        }
      });
    }

    return {
      operation,
      contentType,
      totalItems: items.length,
      successful: results.length,
      failed: errors.length,
      results,
      errors
    };
  }

  /**
   * Handle search content operation
   */
  async handleSearchContent(args, user) {
    const { contentType, query, fields = [], limit = 25 } = args;
    const client = this.createStrapiClient(user);

    // Build search filters
    const filters = {};
    if (fields.length > 0) {
      filters.$or = fields.map(field => ({
        [field]: { $containsi: query }
      }));
    } else {
      // Search in common text fields if no specific fields provided
      filters.$or = [
        { title: { $containsi: query } },
        { name: { $containsi: query } },
        { content: { $containsi: query } },
        { description: { $containsi: query } }
      ];
    }

    const response = await client.get(`/${contentType}`, {
      params: {
        filters,
        pagination: { limit },
        populate: '*'
      }
    });

    return response.data;
  }

  /**
   * Handle get schema operation
   */
  async handleGetSchema(args, user) {
    const { contentType } = args;
    const client = this.createStrapiClient(user);

    try {
      // Try to get content type schema (this might require admin API access)
      const response = await client.get(`/content-manager/content-types/${contentType}`);
      return response.data;
    } catch (error) {
      // Fallback: get a sample item to infer schema
      try {
        const sampleResponse = await client.get(`/${contentType}`, {
          params: { pagination: { limit: 1 } }
        });
        
        if (sampleResponse.data.data && sampleResponse.data.data.length > 0) {
          const sample = sampleResponse.data.data[0];
          return {
            contentType,
            inferredSchema: {
              attributes: Object.keys(sample.attributes || {}),
              sampleData: sample.attributes
            },
            note: 'Schema inferred from sample data'
          };
        }
        
        return { contentType, error: 'No data available to infer schema' };
      } catch (fallbackError) {
        throw new Error(`Cannot retrieve schema for ${contentType}: ${fallbackError.message}`);
      }
    }
  }
}
