import Joi from 'joi';

/**
 * Validation Middleware for MCP Server
 */
export class ValidationMiddleware {
  constructor(logger) {
    this.logger = logger;
    this.schemas = this.initializeSchemas();
  }

  /**
   * Initialize validation schemas
   */
  initializeSchemas() {
    return {
      auth: Joi.object({
        apiKey: Joi.string().required().min(10),
        environment: Joi.string().valid('development', 'production').optional()
      }),

      initialize: Joi.object({
        protocolVersion: Joi.string().optional(),
        capabilities: Joi.object().optional(),
        clientInfo: Joi.object({
          name: Joi.string().required(),
          version: Joi.string().required()
        }).optional()
      }),

      toolCall: Joi.object({
        name: Joi.string().required(),
        arguments: Joi.object().required()
      }),

      strapiContent: Joi.object({
        contentType: Joi.string().required().pattern(/^[a-z][a-z0-9-]*$/),
        id: Joi.string().optional(),
        data: Joi.object().optional(),
        filters: Joi.object().optional(),
        populate: Joi.string().optional(),
        locale: Joi.string().optional().pattern(/^[a-z]{2}(-[A-Z]{2})?$/)
      }),

      bulkOperation: Joi.object({
        operation: Joi.string().valid('create', 'update', 'delete').required(),
        contentType: Joi.string().required().pattern(/^[a-z][a-z0-9-]*$/),
        items: Joi.array().items(Joi.object()).min(1).max(100).required(),
        batchSize: Joi.number().integer().min(1).max(50).default(10)
      }),

      searchContent: Joi.object({
        contentType: Joi.string().required().pattern(/^[a-z][a-z0-9-]*$/),
        query: Joi.string().required().min(1).max(500),
        fields: Joi.array().items(Joi.string()).optional(),
        limit: Joi.number().integer().min(1).max(100).default(25)
      })
    };
  }

  /**
   * Generic validation middleware factory
   */
  validate = (schemaName) => {
    return (req, res, next) => {
      const schema = this.schemas[schemaName];
      if (!schema) {
        this.logger.error('Validation schema not found', { schemaName });
        return res.status(500).json({ error: 'Internal validation error' });
      }

      const { error, value } = schema.validate(req.body, {
        abortEarly: false,
        stripUnknown: true
      });

      if (error) {
        const validationErrors = error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value
        }));

        this.logger.warn('Validation failed', { 
          schemaName, 
          errors: validationErrors 
        });

        return res.status(400).json({
          error: 'Validation failed',
          details: validationErrors
        });
      }

      req.body = value;
      next();
    };
  };

  /**
   * Validate authentication request
   */
  validateAuth = this.validate('auth');

  /**
   * Validate MCP initialization
   */
  validateInitialize = this.validate('initialize');

  /**
   * Validate tool call
   */
  validateToolCall = (req, res, next) => {
    // First validate the basic tool call structure
    const { error: basicError, value: basicValue } = this.schemas.toolCall.validate(req.body);
    
    if (basicError) {
      return res.status(400).json({
        error: 'Invalid tool call format',
        details: basicError.details.map(d => d.message)
      });
    }

    // Then validate tool-specific arguments
    const { name, arguments: args } = basicValue;
    let toolSchema;

    switch (name) {
      case 'strapi_get_content':
      case 'strapi_create_content':
      case 'strapi_update_content':
      case 'strapi_delete_content':
        toolSchema = this.schemas.strapiContent;
        break;
      
      case 'strapi_bulk_operation':
        toolSchema = this.schemas.bulkOperation;
        break;
      
      case 'strapi_search_content':
        toolSchema = this.schemas.searchContent;
        break;
      
      case 'strapi_get_schema':
        toolSchema = Joi.object({
          contentType: Joi.string().required().pattern(/^[a-z][a-z0-9-]*$/)
        });
        break;
      
      default:
        return res.status(400).json({
          error: `Unknown tool: ${name}`
        });
    }

    const { error: argsError, value: argsValue } = toolSchema.validate(args);
    
    if (argsError) {
      this.logger.warn('Tool arguments validation failed', {
        tool: name,
        errors: argsError.details.map(d => d.message)
      });

      return res.status(400).json({
        error: `Invalid arguments for tool ${name}`,
        details: argsError.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value
        }))
      });
    }

    req.body = {
      name,
      arguments: argsValue
    };

    next();
  };

  /**
   * Validate content type name
   */
  validateContentType(contentType) {
    const schema = Joi.string().pattern(/^[a-z][a-z0-9-]*$/).required();
    const { error } = schema.validate(contentType);
    return !error;
  }

  /**
   * Validate locale format
   */
  validateLocale(locale) {
    const schema = Joi.string().pattern(/^[a-z]{2}(-[A-Z]{2})?$/);
    const { error } = schema.validate(locale);
    return !error;
  }

  /**
   * Sanitize HTML content
   */
  sanitizeHtml(content) {
    if (typeof content !== 'string') return content;
    
    // Basic HTML sanitization - remove script tags and dangerous attributes
    return content
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/on\w+="[^"]*"/gi, '')
      .replace(/javascript:/gi, '');
  }

  /**
   * Validate and sanitize content data
   */
  validateContentData(data) {
    if (!data || typeof data !== 'object') {
      throw new Error('Content data must be an object');
    }

    const sanitized = {};
    
    for (const [key, value] of Object.entries(data)) {
      // Validate field names
      if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(key)) {
        throw new Error(`Invalid field name: ${key}`);
      }

      // Sanitize string values
      if (typeof value === 'string') {
        sanitized[key] = this.sanitizeHtml(value);
      } else if (Array.isArray(value)) {
        sanitized[key] = value.map(item => 
          typeof item === 'string' ? this.sanitizeHtml(item) : item
        );
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  /**
   * Validate pagination parameters
   */
  validatePagination(pagination) {
    const schema = Joi.object({
      page: Joi.number().integer().min(1).default(1),
      pageSize: Joi.number().integer().min(1).max(100).default(25),
      start: Joi.number().integer().min(0).optional(),
      limit: Joi.number().integer().min(1).max(100).optional()
    });

    const { error, value } = schema.validate(pagination || {});
    if (error) {
      throw new Error(`Invalid pagination: ${error.message}`);
    }

    return value;
  }

  /**
   * Validate sort parameters
   */
  validateSort(sort) {
    if (!sort) return undefined;

    const schema = Joi.alternatives().try(
      Joi.string().pattern(/^[a-zA-Z][a-zA-Z0-9_]*:(asc|desc)$/),
      Joi.array().items(Joi.string().pattern(/^[a-zA-Z][a-zA-Z0-9_]*:(asc|desc)$/))
    );

    const { error, value } = schema.validate(sort);
    if (error) {
      throw new Error(`Invalid sort parameter: ${error.message}`);
    }

    return value;
  }

  /**
   * Validate filters object
   */
  validateFilters(filters) {
    if (!filters || typeof filters !== 'object') {
      return {};
    }

    // Basic validation - ensure no dangerous operations
    const dangerousKeys = ['$where', '$regex'];
    for (const key of dangerousKeys) {
      if (this.hasDeepKey(filters, key)) {
        throw new Error(`Dangerous filter operation not allowed: ${key}`);
      }
    }

    return filters;
  }

  /**
   * Check if object has a key at any depth
   */
  hasDeepKey(obj, targetKey) {
    if (typeof obj !== 'object' || obj === null) return false;
    
    for (const [key, value] of Object.entries(obj)) {
      if (key === targetKey) return true;
      if (typeof value === 'object' && this.hasDeepKey(value, targetKey)) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * Validate webhook payload
   */
  validateWebhookPayload(payload) {
    const schema = Joi.object({
      event: Joi.string().required(),
      model: Joi.string().required(),
      entry: Joi.object().optional(),
      timestamp: Joi.date().iso().optional()
    });

    const { error, value } = schema.validate(payload);
    if (error) {
      throw new Error(`Invalid webhook payload: ${error.message}`);
    }

    return value;
  }
}
