# n8n Strapi Automation Suite

A comprehensive automation platform for Strapi CMS operations using n8n workflows, MCP (Model Context Protocol) integrations, and OpenAI-powered content processing.

## 🚀 Features

### Core Automation Workflows
- **Enhanced CRUD Operations**: Advanced Strapi content management with error handling
- **Data Migration**: Seamless content transfer between environments
- **ETL Processes**: Extract, Transform, Load workflows for data processing
- **Content Analysis**: AI-powered content insights and quality assessment
- **Content Translation**: Multi-language content generation using OpenAI
- **Content Generation**: AI-powered content creation for various formats

### MCP Integration
- **Protocol Server**: Full MCP server implementation for programmatic access
- **Client Workflows**: n8n workflows that communicate via MCP
- **Authentication**: Secure API key management and JWT tokens
- **Real-time Communication**: WebSocket support for live updates

### OpenAI Integration
- **Content Analysis**: Sentiment, readability, keyword extraction, quality assessment
- **Translation Services**: Multi-language content translation
- **Content Generation**: Articles, blog posts, product descriptions, social media content
- **SEO Optimization**: AI-powered meta descriptions and keyword suggestions

## 📁 Project Structure

```
n8n-docker/
├── docker-compose.yml          # Main Docker configuration
├── templates/                  # n8n workflow templates
│   ├── strapi-crud-enhanced.json
│   ├── strapi-data-migration.json
│   ├── strapi-etl-extract.json
│   ├── strapi-etl-transform.json
│   ├── strapi-etl-load.json
│   ├── strapi-openai-analysis.json
│   ├── strapi-openai-translation.json
│   ├── strapi-openai-content-generation.json
│   └── strapi-mcp-client.json
├── mcp-server/                 # MCP server implementation
│   ├── server.js
│   ├── package.json
│   ├── Dockerfile
│   └── src/
│       ├── protocol-handler.js
│       ├── strapi-connector.js
│       ├── auth-middleware.js
│       └── validation-middleware.js
├── config/                     # Configuration templates
│   └── environment-template.env
└── README.md                   # This file
```

## 🛠 Quick Start

### Prerequisites
- Docker and Docker Compose
- Strapi CMS instances (development and production)
- OpenAI API key
- Basic understanding of n8n workflows

### 1. Environment Setup

1. Copy the environment template:
```bash
cp config/environment-template.env .env
```

2. Edit `.env` with your actual values:
```bash
# Strapi Configuration
STRAPI_DEV_URL=http://localhost:1337
STRAPI_DEV_TOKEN=your_dev_token_here
STRAPI_PROD_URL=https://your-prod-strapi.com
STRAPI_PROD_TOKEN=your_prod_token_here

# OpenAI Configuration
OPENAI_API_KEY=sk-your_openai_key_here

# MCP Server Configuration
JWT_SECRET=your_jwt_secret_here
MCP_DEV_API_KEY=your_dev_api_key
MCP_PROD_API_KEY=your_prod_api_key
```

### 2. Start Services

```bash
docker-compose up -d
```

This will start:
- n8n on port 5678
- PostgreSQL database
- MCP server on port 3001

### 3. Access n8n

1. Open http://localhost:5678
2. Login with credentials from your `.env` file
3. Import workflow templates from the `templates/` directory

### 4. Import Workflow Templates

1. In n8n, go to **Workflows** → **Import from File**
2. Import each template from the `templates/` directory
3. Configure credentials for:
   - Strapi API (development and production)
   - OpenAI API
   - MCP Server

## 📋 Workflow Templates

### 1. Enhanced CRUD Operations (`strapi-crud-enhanced.json`)
**Purpose**: Advanced Strapi content management with comprehensive error handling

**Features**:
- Multi-environment support (dev/prod)
- Dynamic content type handling
- Comprehensive error handling
- Response formatting

**Usage**:
```bash
curl -X POST http://localhost:5678/webhook/strapi-crud-webhook \
  -H "Content-Type: application/json" \
  -d '{
    "operation": "create",
    "contentType": "articles",
    "environment": "development",
    "data": {
      "title": "Test Article",
      "content": "Article content here"
    }
  }'
```

### 2. Data Migration (`strapi-data-migration.json`)
**Purpose**: Transfer content between Strapi environments

**Features**:
- Batch processing
- Data validation
- Dry run mode
- Progress tracking
- Error reporting

**Usage**:
- Trigger manually in n8n
- Configure source/target environments
- Set batch size and content types
- Monitor progress via email notifications

### 3. ETL Workflows

#### Extract (`strapi-etl-extract.json`)
- Scheduled data extraction
- Incremental and full extraction modes
- Pagination handling
- Multiple output formats

#### Transform (`strapi-etl-transform.json`)
- Field mapping and data enrichment
- Format conversion
- Data validation
- Error handling

#### Load (`strapi-etl-load.json`)
- Batch loading with conflict resolution
- Dry run mode
- Progress tracking
- Rollback capabilities

### 4. OpenAI Integration

#### Content Analysis (`strapi-openai-analysis.json`)
**Analysis Types**:
- Sentiment analysis
- Readability assessment
- Keyword extraction
- Quality evaluation

#### Translation (`strapi-openai-translation.json`)
**Features**:
- Multi-language support
- Batch translation
- Format preservation
- Quality validation

#### Content Generation (`strapi-openai-content-generation.json`)
**Content Types**:
- Articles and blog posts
- Product descriptions
- Social media content
- Email campaigns

### 5. MCP Client (`strapi-mcp-client.json`)
**Purpose**: Communicate with Strapi via MCP protocol

**Operations**:
- Content CRUD operations
- Bulk operations
- Schema introspection
- Search functionality

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `STRAPI_DEV_URL` | Development Strapi URL | Yes |
| `STRAPI_DEV_TOKEN` | Development API token | Yes |
| `STRAPI_PROD_URL` | Production Strapi URL | Yes |
| `STRAPI_PROD_TOKEN` | Production API token | Yes |
| `OPENAI_API_KEY` | OpenAI API key | Yes |
| `JWT_SECRET` | JWT signing secret | Yes |
| `MCP_DEV_API_KEY` | MCP development API key | Yes |
| `MCP_PROD_API_KEY` | MCP production API key | Yes |

### n8n Credentials

Configure these credentials in n8n:

1. **Strapi API** (for each environment)
   - Type: HTTP Header Auth
   - Header: Authorization
   - Value: Bearer YOUR_TOKEN

2. **OpenAI API**
   - Type: HTTP Header Auth
   - Header: Authorization
   - Value: Bearer YOUR_OPENAI_KEY

3. **MCP Server**
   - Type: HTTP Header Auth
   - Header: Authorization
   - Value: Bearer YOUR_MCP_TOKEN

## 🔍 Monitoring and Logging

### Health Checks
- n8n: http://localhost:5678/healthz
- MCP Server: http://localhost:3001/health

### Logs
- n8n logs: `docker-compose logs n8n`
- MCP server logs: `docker-compose logs mcp-server`
- Database logs: `docker-compose logs db`

### Metrics
- Workflow execution statistics in n8n dashboard
- MCP server metrics at http://localhost:3001/metrics
- Custom logging in workflow nodes

## 🚨 Troubleshooting

### Common Issues

1. **Workflow Import Errors**
   - Ensure all required credentials are configured
   - Check environment variable names match template

2. **API Connection Failures**
   - Verify Strapi URLs and tokens
   - Check network connectivity
   - Validate SSL certificates for HTTPS endpoints

3. **OpenAI Rate Limits**
   - Monitor token usage in workflow logs
   - Implement retry logic with exponential backoff
   - Consider upgrading OpenAI plan

4. **MCP Server Issues**
   - Check server logs for authentication errors
   - Verify JWT secret configuration
   - Ensure proper API key format

### Performance Optimization

1. **Batch Processing**
   - Adjust batch sizes based on content complexity
   - Monitor memory usage during large operations
   - Use pagination for large datasets

2. **Caching**
   - Implement Redis for frequently accessed data
   - Cache OpenAI responses for similar requests
   - Use Strapi's built-in caching mechanisms

3. **Resource Management**
   - Monitor Docker container resources
   - Scale services based on load
   - Optimize database queries

## 📚 Advanced Usage

### Custom Workflow Development

1. **Creating New Templates**
   - Use existing templates as starting points
   - Follow naming conventions
   - Include comprehensive error handling
   - Document webhook endpoints and parameters

2. **Extending MCP Server**
   - Add new tools in `protocol-handler.js`
   - Implement custom validation rules
   - Add new authentication methods
   - Create custom connectors

3. **OpenAI Integration**
   - Experiment with different models (GPT-4, GPT-3.5)
   - Customize prompts for specific use cases
   - Implement fine-tuning for domain-specific content
   - Add custom analysis types

### Integration Patterns

1. **Event-Driven Architecture**
   - Use webhooks for real-time processing
   - Implement event queues for reliability
   - Create event-driven content workflows

2. **Microservices Integration**
   - Connect with external APIs
   - Implement service mesh patterns
   - Use circuit breakers for resilience

3. **Data Pipeline Orchestration**
   - Chain ETL workflows
   - Implement data quality checks
   - Create automated data lineage tracking

## 🤝 Contributing

1. Fork the repository
2. Create feature branches
3. Follow coding standards
4. Add comprehensive tests
5. Update documentation
6. Submit pull requests

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- Create issues for bugs and feature requests
- Check existing documentation and troubleshooting guides
- Join the community discussions
- Contact maintainers for enterprise support

---

**Happy Automating! 🚀**
