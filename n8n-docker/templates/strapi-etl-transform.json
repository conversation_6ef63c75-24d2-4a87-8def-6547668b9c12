{"name": "Strapi ETL - Transform", "nodes": [{"parameters": {"httpMethod": "POST", "path": "transform-data", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "sourceFile", "value": "={{ $json.sourceFile || '' }}"}, {"name": "transformationType", "value": "={{ $json.transformationType || 'field_mapping' }}"}, {"name": "targetContentType", "value": "={{ $json.targetContentType || 'articles' }}"}, {"name": "transformationRules", "value": "={{ JSON.stringify($json.transformationRules || {}) }}"}, {"name": "validationRules", "value": "={{ JSON.stringify($json.validationRules || {}) }}"}, {"name": "outputFormat", "value": "={{ $json.outputFormat || 'json' }}"}]}, "options": {}}, "id": "extract-parameters", "name": "Extract Parameters", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"jsCode": "// Load source data for transformation\nconst params = $node['Extract Parameters'].json;\nconst sourceFile = params.sourceFile;\n\n// For demo purposes, we'll simulate loading data\n// In real implementation, this would load from file system or database\nlet sourceData;\n\nif (sourceFile) {\n  // Simulate loading from file\n  console.log(`Loading data from: ${sourceFile}`);\n  // In real implementation: sourceData = loadFromFile(sourceFile);\n  \n  // For now, use sample data or data from webhook\n  sourceData = $input.first().json.data || [\n    {\n      id: 1,\n      attributes: {\n        title: \"Sample Article\",\n        content: \"This is sample content\",\n        author: \"<PERSON>\",\n        category: \"Technology\",\n        tags: [\"tech\", \"sample\"],\n        publishedAt: \"2024-01-01T00:00:00.000Z\"\n      }\n    }\n  ];\n} else {\n  // Use data from webhook payload\n  sourceData = $input.first().json.data || [];\n}\n\nif (!Array.isArray(sourceData)) {\n  sourceData = [sourceData];\n}\n\nconst result = {\n  sourceData,\n  totalItems: sourceData.length,\n  transformationType: params.transformationType,\n  targetContentType: params.targetContentType,\n  transformationRules: JSON.parse(params.transformationRules),\n  validationRules: JSON.parse(params.validationRules),\n  loadedAt: new Date().toISOString()\n};\n\nconsole.log(`Loaded ${sourceData.length} items for transformation`);\nreturn [{ json: result }];"}, "id": "load-source-data", "name": "Load Source Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"jsCode": "// Apply transformation rules to data\nconst { sourceData, transformationType, transformationRules, targetContentType } = $json;\nconst transformedItems = [];\nconst errors = [];\n\nfor (let i = 0; i < sourceData.length; i++) {\n  const item = sourceData[i];\n  \n  try {\n    let transformedItem = {\n      id: item.id,\n      attributes: {},\n      meta: {\n        sourceId: item.id,\n        transformedAt: new Date().toISOString(),\n        transformationType,\n        targetContentType\n      }\n    };\n\n    switch (transformationType) {\n      case 'field_mapping':\n        transformedItem = applyFieldMapping(item, transformationRules, transformedItem);\n        break;\n        \n      case 'data_enrichment':\n        transformedItem = applyDataEnrichment(item, transformationRules, transformedItem);\n        break;\n        \n      case 'format_conversion':\n        transformedItem = applyFormatConversion(item, transformationRules, transformedItem);\n        break;\n        \n      case 'content_restructure':\n        transformedItem = applyContentRestructure(item, transformationRules, transformedItem);\n        break;\n        \n      default:\n        // Default: copy all attributes\n        transformedItem.attributes = { ...item.attributes };\n    }\n\n    transformedItems.push(transformedItem);\n    \n  } catch (error) {\n    errors.push({\n      itemIndex: i,\n      itemId: item.id,\n      error: error.message,\n      originalItem: item\n    });\n  }\n}\n\n// Transformation helper functions\nfunction applyFieldMapping(item, rules, transformedItem) {\n  const fieldMappings = rules.fieldMappings || {};\n  \n  for (const [sourceField, targetField] of Object.entries(fieldMappings)) {\n    if (item.attributes && item.attributes[sourceField] !== undefined) {\n      transformedItem.attributes[targetField] = item.attributes[sourceField];\n    }\n  }\n  \n  // Copy unmapped fields if specified\n  if (rules.copyUnmappedFields) {\n    for (const [key, value] of Object.entries(item.attributes || {})) {\n      if (!fieldMappings[key] && transformedItem.attributes[key] === undefined) {\n        transformedItem.attributes[key] = value;\n      }\n    }\n  }\n  \n  return transformedItem;\n}\n\nfunction applyDataEnrichment(item, rules, transformedItem) {\n  // Copy original attributes\n  transformedItem.attributes = { ...item.attributes };\n  \n  // Apply enrichment rules\n  const enrichmentRules = rules.enrichmentRules || {};\n  \n  for (const [field, enrichmentRule] of Object.entries(enrichmentRules)) {\n    switch (enrichmentRule.type) {\n      case 'computed':\n        transformedItem.attributes[field] = computeField(item, enrichmentRule.formula);\n        break;\n        \n      case 'lookup':\n        transformedItem.attributes[field] = lookupValue(item, enrichmentRule.lookupTable);\n        break;\n        \n      case 'default':\n        if (!transformedItem.attributes[field]) {\n          transformedItem.attributes[field] = enrichmentRule.defaultValue;\n        }\n        break;\n    }\n  }\n  \n  return transformedItem;\n}\n\nfunction applyFormatConversion(item, rules, transformedItem) {\n  transformedItem.attributes = { ...item.attributes };\n  \n  const formatRules = rules.formatRules || {};\n  \n  for (const [field, formatRule] of Object.entries(formatRules)) {\n    if (transformedItem.attributes[field] !== undefined) {\n      switch (formatRule.type) {\n        case 'date':\n          transformedItem.attributes[field] = formatDate(transformedItem.attributes[field], formatRule.format);\n          break;\n          \n        case 'string':\n          transformedItem.attributes[field] = formatString(transformedItem.attributes[field], formatRule.format);\n          break;\n          \n        case 'number':\n          transformedItem.attributes[field] = formatNumber(transformedItem.attributes[field], formatRule.format);\n          break;\n      }\n    }\n  }\n  \n  return transformedItem;\n}\n\nfunction applyContentRestructure(item, rules, transformedItem) {\n  const restructureRules = rules.restructureRules || {};\n  \n  // Apply restructuring logic\n  if (restructureRules.flattenArrays) {\n    for (const [key, value] of Object.entries(item.attributes || {})) {\n      if (Array.isArray(value) && restructureRules.flattenArrays.includes(key)) {\n        transformedItem.attributes[key] = value.join(', ');\n      } else {\n        transformedItem.attributes[key] = value;\n      }\n    }\n  } else {\n    transformedItem.attributes = { ...item.attributes };\n  }\n  \n  return transformedItem;\n}\n\n// Helper functions\nfunction computeField(item, formula) {\n  // Simple formula evaluation (extend as needed)\n  if (formula === 'slug_from_title') {\n    return item.attributes?.title?.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '') || '';\n  }\n  return formula;\n}\n\nfunction lookupValue(item, lookupTable) {\n  // Simple lookup implementation\n  const key = item.attributes?.category || '';\n  return lookupTable[key] || key;\n}\n\nfunction formatDate(value, format) {\n  if (!value) return value;\n  const date = new Date(value);\n  return date.toISOString();\n}\n\nfunction formatString(value, format) {\n  if (!value || typeof value !== 'string') return value;\n  \n  switch (format) {\n    case 'uppercase':\n      return value.toUpperCase();\n    case 'lowercase':\n      return value.toLowerCase();\n    case 'title_case':\n      return value.replace(/\\w\\S*/g, (txt) => \n        txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());\n    default:\n      return value;\n  }\n}\n\nfunction formatNumber(value, format) {\n  if (value === null || value === undefined) return value;\n  const num = parseFloat(value);\n  if (isNaN(num)) return value;\n  \n  switch (format) {\n    case 'integer':\n      return Math.round(num);\n    case 'currency':\n      return num.toFixed(2);\n    default:\n      return num;\n  }\n}\n\nconst result = {\n  transformedItems,\n  errors,\n  totalItems: sourceData.length,\n  successfulTransformations: transformedItems.length,\n  failedTransformations: errors.length,\n  transformationType,\n  targetContentType,\n  transformedAt: new Date().toISOString()\n};\n\nconsole.log(`Transformation completed: ${transformedItems.length} successful, ${errors.length} failed`);\nreturn [{ json: result }];"}, "id": "apply-transformations", "name": "Apply Transformations", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"jsCode": "// Validate transformed data\nconst { transformedItems, validationRules } = $node['Load Source Data'].json;\nconst transformResult = $json;\n\nconst validatedItems = [];\nconst validationErrors = [];\n\nfor (let i = 0; i < transformResult.transformedItems.length; i++) {\n  const item = transformResult.transformedItems[i];\n  const validationResult = validateItem(item, validationRules);\n  \n  if (validationResult.isValid) {\n    validatedItems.push({\n      ...item,\n      validation: {\n        status: 'passed',\n        validatedAt: new Date().toISOString()\n      }\n    });\n  } else {\n    validationErrors.push({\n      itemIndex: i,\n      itemId: item.id,\n      errors: validationResult.errors,\n      item\n    });\n  }\n}\n\nfunction validateItem(item, rules) {\n  const errors = [];\n  \n  // Required fields validation\n  if (rules.requiredFields) {\n    for (const field of rules.requiredFields) {\n      if (!item.attributes[field] || item.attributes[field] === '') {\n        errors.push(`Required field '${field}' is missing or empty`);\n      }\n    }\n  }\n  \n  // Data type validation\n  if (rules.dataTypes) {\n    for (const [field, expectedType] of Object.entries(rules.dataTypes)) {\n      const value = item.attributes[field];\n      if (value !== undefined && value !== null) {\n        const actualType = typeof value;\n        if (expectedType === 'array' && !Array.isArray(value)) {\n          errors.push(`Field '${field}' should be an array`);\n        } else if (expectedType !== 'array' && actualType !== expectedType) {\n          errors.push(`Field '${field}' should be ${expectedType}, got ${actualType}`);\n        }\n      }\n    }\n  }\n  \n  // Length validation\n  if (rules.fieldLengths) {\n    for (const [field, lengthRule] of Object.entries(rules.fieldLengths)) {\n      const value = item.attributes[field];\n      if (value && typeof value === 'string') {\n        if (lengthRule.min && value.length < lengthRule.min) {\n          errors.push(`Field '${field}' is too short (min: ${lengthRule.min})`);\n        }\n        if (lengthRule.max && value.length > lengthRule.max) {\n          errors.push(`Field '${field}' is too long (max: ${lengthRule.max})`);\n        }\n      }\n    }\n  }\n  \n  // Pattern validation\n  if (rules.patterns) {\n    for (const [field, pattern] of Object.entries(rules.patterns)) {\n      const value = item.attributes[field];\n      if (value && typeof value === 'string') {\n        const regex = new RegExp(pattern);\n        if (!regex.test(value)) {\n          errors.push(`Field '${field}' does not match required pattern`);\n        }\n      }\n    }\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors\n  };\n}\n\nconst result = {\n  ...transformResult,\n  validatedItems,\n  validationErrors,\n  totalValidated: validatedItems.length,\n  totalValidationErrors: validationErrors.length,\n  validationCompletedAt: new Date().toISOString()\n};\n\nconsole.log(`Validation completed: ${validatedItems.length} valid, ${validationErrors.length} invalid`);\nreturn [{ json: result }];"}, "id": "validate-data", "name": "Validate Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"conditions": {"number": [{"value1": "={{ $json.totalValidationErrors }}", "operation": "equal", "value2": 0}]}}, "id": "validation-check", "name": "Validation Check", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"jsCode": "// Prepare final output\nconst validationResult = $json;\nconst outputFormat = $node['Extract Parameters'].json.outputFormat;\nconst timestamp = new Date().toISOString().replace(/[:.]/g, '-');\nconst filename = `transformed_${validationResult.targetContentType}_${timestamp}.${outputFormat}`;\n\n// Prepare transformed data for output\nconst outputData = {\n  metadata: {\n    transformationType: validationResult.transformationType,\n    targetContentType: validationResult.targetContentType,\n    totalItems: validationResult.totalItems,\n    successfulTransformations: validationResult.successfulTransformations,\n    validatedItems: validationResult.totalValidated,\n    transformedAt: validationResult.transformedAt,\n    validatedAt: validationResult.validationCompletedAt\n  },\n  data: validationResult.validatedItems.map(item => ({\n    id: item.id,\n    attributes: item.attributes,\n    meta: item.meta\n  }))\n};\n\nconst result = {\n  ...validationResult,\n  output: {\n    filename,\n    format: outputFormat,\n    data: JSON.stringify(outputData, null, 2),\n    size: JSON.stringify(outputData).length,\n    itemCount: outputData.data.length\n  },\n  summary: {\n    status: 'success',\n    totalProcessed: validationResult.totalItems,\n    successful: validationResult.totalValidated,\n    failed: validationResult.totalValidationErrors + validationResult.failedTransformations,\n    outputFile: filename\n  }\n};\n\nconsole.log(`Transformation pipeline completed successfully: ${result.output.itemCount} items ready for load`);\nreturn [{ json: result }];"}, "id": "prepare-success-output", "name": "Prepare Success Output", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 200]}, {"parameters": {"jsCode": "// Handle validation errors\nconst validationResult = $json;\nconst timestamp = new Date().toISOString().replace(/[:.]/g, '-');\nconst errorFilename = `transform_errors_${validationResult.targetContentType}_${timestamp}.json`;\n\n// Compile all errors\nconst allErrors = {\n  transformationErrors: validationResult.errors || [],\n  validationErrors: validationResult.validationErrors || []\n};\n\nconst errorReport = {\n  metadata: {\n    transformationType: validationResult.transformationType,\n    targetContentType: validationResult.targetContentType,\n    totalItems: validationResult.totalItems,\n    totalErrors: allErrors.transformationErrors.length + allErrors.validationErrors.length,\n    reportGeneratedAt: new Date().toISOString()\n  },\n  errors: allErrors,\n  validItems: validationResult.validatedItems || []\n};\n\nconst result = {\n  ...validationResult,\n  output: {\n    filename: errorFilename,\n    format: 'json',\n    data: JSON.stringify(errorReport, null, 2),\n    size: JSON.stringify(errorReport).length,\n    validItemCount: errorReport.validItems.length\n  },\n  summary: {\n    status: 'partial_success',\n    totalProcessed: validationResult.totalItems,\n    successful: errorReport.validItems.length,\n    failed: errorReport.metadata.totalErrors,\n    errorFile: errorFilename\n  }\n};\n\nconsole.log(`Transformation completed with errors: ${result.output.validItemCount} valid items, ${errorReport.metadata.totalErrors} errors`);\nreturn [{ json: result }];"}, "id": "handle-validation-errors", "name": "Handle Validation Errors", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 400]}, {"parameters": {"values": {"string": [{"name": "status", "value": "={{ $json.summary.status }}"}, {"name": "totalProcessed", "value": "={{ $json.summary.totalProcessed }}"}, {"name": "successful", "value": "={{ $json.summary.successful }}"}, {"name": "failed", "value": "={{ $json.summary.failed }}"}, {"name": "outputFile", "value": "={{ $json.output.filename }}"}, {"name": "transformationType", "value": "={{ $json.transformationType }}"}, {"name": "targetContentType", "value": "={{ $json.targetContentType }}"}, {"name": "completedAt", "value": "={{ new Date().toISOString() }}"}]}, "options": {}}, "id": "transformation-summary", "name": "Transformation Summary", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1780, 300]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Extract Parameters", "type": "main", "index": 0}]]}, "Extract Parameters": {"main": [[{"node": "Load Source Data", "type": "main", "index": 0}]]}, "Load Source Data": {"main": [[{"node": "Apply Transformations", "type": "main", "index": 0}]]}, "Apply Transformations": {"main": [[{"node": "Validate Data", "type": "main", "index": 0}]]}, "Validate Data": {"main": [[{"node": "Validation Check", "type": "main", "index": 0}]]}, "Validation Check": {"main": [[{"node": "Prepare Success Output", "type": "main", "index": 0}], [{"node": "Handle Validation Errors", "type": "main", "index": 0}]]}, "Prepare Success Output": {"main": [[{"node": "Transformation Summary", "type": "main", "index": 0}]]}, "Handle Validation Errors": {"main": [[{"node": "Transformation Summary", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1"}