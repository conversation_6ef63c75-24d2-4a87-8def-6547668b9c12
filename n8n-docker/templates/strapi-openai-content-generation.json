{"name": "Strapi OpenAI Content Generation", "nodes": [{"parameters": {"httpMethod": "POST", "path": "generate-content", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "contentType", "value": "={{ $json.contentType || 'articles' }}"}, {"name": "generationType", "value": "={{ $json.generationType || 'article' }}"}, {"name": "topic", "value": "={{ $json.topic || '' }}"}, {"name": "keywords", "value": "={{ JSON.stringify($json.keywords || []) }}"}, {"name": "tone", "value": "={{ $json.tone || 'professional' }}"}, {"name": "length", "value": "={{ $json.length || 'medium' }}"}, {"name": "language", "value": "={{ $json.language || 'en' }}"}, {"name": "targetAudience", "value": "={{ $json.targetAudience || 'general' }}"}, {"name": "includeImages", "value": "={{ $json.includeImages || 'false' }}"}, {"name": "environment", "value": "={{ $json.environment || 'development' }}"}]}, "options": {}}, "id": "extract-parameters", "name": "Extract Parameters", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"jsCode": "// Generate content prompt based on parameters\nconst params = $node['Extract Parameters'].json;\nconst keywords = JSON.parse(params.keywords);\n\n// Content length mapping\nconst lengthMapping = {\n  'short': '300-500 words',\n  'medium': '800-1200 words',\n  'long': '1500-2500 words',\n  'brief': '100-200 words'\n};\n\n// Tone guidelines\nconst toneGuidelines = {\n  'professional': 'formal, authoritative, and informative',\n  'casual': 'friendly, conversational, and approachable',\n  'technical': 'precise, detailed, and expert-level',\n  'creative': 'engaging, imaginative, and inspiring',\n  'educational': 'clear, instructional, and easy to understand'\n};\n\nlet systemPrompt = '';\nlet userPrompt = '';\n\nswitch (params.generationType) {\n  case 'article':\n    systemPrompt = `You are an expert content writer specializing in creating high-quality articles. Write in a ${toneGuidelines[params.tone] || 'professional'} tone for ${params.targetAudience} audience. The content should be ${lengthMapping[params.length] || 'medium length'} and optimized for SEO.`;\n    \n    userPrompt = `Write a comprehensive article about \"${params.topic}\".\\n\\n`;\n    \n    if (keywords.length > 0) {\n      userPrompt += `Include these keywords naturally: ${keywords.join(', ')}\\n\\n`;\n    }\n    \n    userPrompt += `Requirements:\n- Create an engaging title\n- Write a compelling introduction\n- Organize content with clear headings and subheadings\n- Include practical examples or case studies\n- End with a strong conclusion\n- Ensure the content is ${lengthMapping[params.length] || 'medium length'}\n- Write in ${params.language === 'en' ? 'English' : params.language}\n\nReturn the content in this JSON format:\n{\n  \"title\": \"Article title\",\n  \"slug\": \"url-friendly-slug\",\n  \"excerpt\": \"Brief summary (150-200 words)\",\n  \"content\": \"Full article content with HTML formatting\",\n  \"meta_description\": \"SEO meta description (150-160 characters)\",\n  \"tags\": [\"tag1\", \"tag2\", \"tag3\"],\n  \"estimated_reading_time\": \"X minutes\"\n}`;\n    break;\n    \n  case 'product_description':\n    systemPrompt = `You are an expert copywriter specializing in product descriptions that convert. Write compelling, benefit-focused descriptions that highlight value propositions and address customer pain points.`;\n    \n    userPrompt = `Create a product description for: \"${params.topic}\"\\n\\n`;\n    \n    if (keywords.length > 0) {\n      userPrompt += `Key features/benefits to highlight: ${keywords.join(', ')}\\n\\n`;\n    }\n    \n    userPrompt += `Return the content in this JSON format:\n{\n  \"title\": \"Product name\",\n  \"short_description\": \"Brief product summary (50-100 words)\",\n  \"long_description\": \"Detailed product description with benefits\",\n  \"features\": [\"feature1\", \"feature2\", \"feature3\"],\n  \"benefits\": [\"benefit1\", \"benefit2\", \"benefit3\"],\n  \"specifications\": {},\n  \"meta_description\": \"SEO meta description\"\n}`;\n    break;\n    \n  case 'blog_post':\n    systemPrompt = `You are a skilled blog writer who creates engaging, shareable content. Focus on storytelling, practical advice, and reader engagement.`;\n    \n    userPrompt = `Write a blog post about \"${params.topic}\".\\n\\n`;\n    \n    if (keywords.length > 0) {\n      userPrompt += `Include these topics/keywords: ${keywords.join(', ')}\\n\\n`;\n    }\n    \n    userPrompt += `Return the content in this JSON format:\n{\n  \"title\": \"Blog post title\",\n  \"slug\": \"url-friendly-slug\",\n  \"introduction\": \"Engaging opening paragraph\",\n  \"content\": \"Main blog content with HTML formatting\",\n  \"conclusion\": \"Strong closing paragraph with call-to-action\",\n  \"meta_description\": \"SEO meta description\",\n  \"categories\": [\"category1\", \"category2\"],\n  \"tags\": [\"tag1\", \"tag2\", \"tag3\"]\n}`;\n    break;\n    \n  case 'social_media':\n    systemPrompt = `You are a social media content specialist who creates engaging, platform-optimized content that drives engagement and shares.`;\n    \n    userPrompt = `Create social media content about \"${params.topic}\".\\n\\n`;\n    \n    userPrompt += `Return the content in this JSON format:\n{\n  \"facebook_post\": \"Facebook-optimized post (up to 500 characters)\",\n  \"twitter_post\": \"Twitter-optimized post (up to 280 characters)\",\n  \"linkedin_post\": \"LinkedIn professional post (up to 1300 characters)\",\n  \"instagram_caption\": \"Instagram caption with hashtags\",\n  \"hashtags\": [\"#hashtag1\", \"#hashtag2\", \"#hashtag3\"],\n  \"call_to_action\": \"Engaging CTA\"\n}`;\n    break;\n    \n  case 'email_campaign':\n    systemPrompt = `You are an email marketing specialist who creates compelling email campaigns that drive opens, clicks, and conversions.`;\n    \n    userPrompt = `Create an email campaign about \"${params.topic}\".\\n\\n`;\n    \n    userPrompt += `Return the content in this JSON format:\n{\n  \"subject_line\": \"Compelling email subject\",\n  \"preview_text\": \"Email preview text\",\n  \"header\": \"Email header/greeting\",\n  \"body\": \"Main email content with HTML formatting\",\n  \"call_to_action\": \"Primary CTA button text\",\n  \"footer\": \"Email footer content\",\n  \"personalization_tags\": [\"tag1\", \"tag2\"]\n}`;\n    break;\n    \n  default:\n    systemPrompt = `You are a versatile content creator who can adapt to any content type and audience. Create high-quality, engaging content that meets the specified requirements.`;\n    \n    userPrompt = `Create content about \"${params.topic}\" for ${params.generationType}.\\n\\nReturn the content in a structured JSON format appropriate for the content type.`;\n}\n\nreturn [{\n  json: {\n    ...params,\n    systemPrompt,\n    userPrompt,\n    keywords: JSON.parse(params.keywords)\n  }\n}];"}, "id": "generate-content-prompt", "name": "Generate Content Prompt", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"method": "POST", "url": "https://api.openai.com/v1/chat/completions", "options": {"response": {"response": {"responseFormat": "json"}}, "timeout": 120000}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"gpt-4\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"{{ $json.systemPrompt }}\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"{{ $json.userPrompt }}\"\n    }\n  ],\n  \"temperature\": 0.7,\n  \"max_tokens\": 3000,\n  \"response_format\": { \"type\": \"json_object\" }\n}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $env.OPENAI_API_KEY }}"}, {"name": "Content-Type", "value": "application/json"}]}}, "id": "openai-generation", "name": "OpenAI Content Generation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 300]}, {"parameters": {"jsCode": "// Process OpenAI response and prepare for Strapi\nconst openaiResponse = $input.first().json;\nconst params = $node['Generate Content Prompt'].json;\n\ntry {\n  const generatedContent = JSON.parse(openaiResponse.choices[0].message.content);\n  \n  // Prepare content for Strapi based on content type\n  let strapiData = {};\n  \n  switch (params.generationType) {\n    case 'article':\n      strapiData = {\n        title: generatedContent.title,\n        slug: generatedContent.slug,\n        content: generatedContent.content,\n        excerpt: generatedContent.excerpt,\n        meta_description: generatedContent.meta_description,\n        tags: generatedContent.tags,\n        estimated_reading_time: generatedContent.estimated_reading_time,\n        status: 'draft',\n        generated_by_ai: true,\n        generation_prompt: params.topic,\n        generation_keywords: params.keywords\n      };\n      break;\n      \n    case 'product_description':\n      strapiData = {\n        name: generatedContent.title,\n        short_description: generatedContent.short_description,\n        description: generatedContent.long_description,\n        features: generatedContent.features,\n        benefits: generatedContent.benefits,\n        specifications: generatedContent.specifications,\n        meta_description: generatedContent.meta_description,\n        generated_by_ai: true\n      };\n      break;\n      \n    case 'blog_post':\n      strapiData = {\n        title: generatedContent.title,\n        slug: generatedContent.slug,\n        introduction: generatedContent.introduction,\n        content: generatedContent.content,\n        conclusion: generatedContent.conclusion,\n        meta_description: generatedContent.meta_description,\n        categories: generatedContent.categories,\n        tags: generatedContent.tags,\n        status: 'draft',\n        generated_by_ai: true\n      };\n      break;\n      \n    case 'social_media':\n      strapiData = {\n        topic: params.topic,\n        facebook_post: generatedContent.facebook_post,\n        twitter_post: generatedContent.twitter_post,\n        linkedin_post: generatedContent.linkedin_post,\n        instagram_caption: generatedContent.instagram_caption,\n        hashtags: generatedContent.hashtags,\n        call_to_action: generatedContent.call_to_action,\n        generated_by_ai: true\n      };\n      break;\n      \n    case 'email_campaign':\n      strapiData = {\n        campaign_name: params.topic,\n        subject_line: generatedContent.subject_line,\n        preview_text: generatedContent.preview_text,\n        header: generatedContent.header,\n        body: generatedContent.body,\n        call_to_action: generatedContent.call_to_action,\n        footer: generatedContent.footer,\n        personalization_tags: generatedContent.personalization_tags,\n        status: 'draft',\n        generated_by_ai: true\n      };\n      break;\n      \n    default:\n      strapiData = {\n        ...generatedContent,\n        generated_by_ai: true,\n        generation_type: params.generationType,\n        generation_topic: params.topic\n      };\n  }\n  \n  // Add common metadata\n  strapiData.ai_generation_metadata = {\n    model: 'gpt-4',\n    generation_type: params.generationType,\n    topic: params.topic,\n    keywords: params.keywords,\n    tone: params.tone,\n    length: params.length,\n    language: params.language,\n    target_audience: params.targetAudience,\n    tokens_used: openaiResponse.usage?.total_tokens || 0,\n    generated_at: new Date().toISOString()\n  };\n  \n  const result = {\n    contentType: params.contentType,\n    generationType: params.generationType,\n    environment: params.environment,\n    strapiData,\n    generatedContent,\n    metadata: strapiData.ai_generation_metadata,\n    success: true\n  };\n  \n  return [{ json: result }];\n  \n} catch (error) {\n  console.error('Content generation processing error:', error);\n  \n  return [{\n    json: {\n      contentType: params.contentType,\n      generationType: params.generationType,\n      environment: params.environment,\n      success: false,\n      error: 'Failed to process generated content',\n      error_details: error.message,\n      raw_response: openaiResponse.choices[0]?.message?.content || 'No response'\n    }\n  }];\n}"}, "id": "process-generated-content", "name": "Process Generated Content", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.success }}", "operation": "equal", "value2": true}]}}, "id": "generation-success-check", "name": "Generation Success Check", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"method": "POST", "url": "={{ $json.environment === 'production' ? $env.STRAPI_PROD_URL : $env.STRAPI_DEV_URL }}/api/{{ $json.contentType }}", "options": {"response": {"response": {"responseFormat": "json"}}}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"data\": {{ JSON.stringify($json.strapiData) }}\n}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $json.environment === 'production' ? $env.STRAPI_PROD_TOKEN : $env.STRAPI_DEV_TOKEN }}"}, {"name": "Content-Type", "value": "application/json"}]}}, "id": "save-to-strapi", "name": "Save to Strapi", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1560, 200]}, {"parameters": {"values": {"string": [{"name": "status", "value": "success"}, {"name": "message", "value": "Content generated and saved successfully"}, {"name": "contentId", "value": "={{ $json.data.id }}"}, {"name": "generationType", "value": "={{ $node['Process Generated Content'].json.generationType }}"}, {"name": "contentType", "value": "={{ $node['Process Generated Content'].json.contentType }}"}, {"name": "tokensUsed", "value": "={{ $node['Process Generated Content'].json.metadata.tokens_used }}"}]}, "options": {}}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1780, 200]}, {"parameters": {"values": {"string": [{"name": "status", "value": "error"}, {"name": "message", "value": "Content generation failed"}, {"name": "error", "value": "={{ $json.error || 'Unknown error' }}"}, {"name": "error_details", "value": "={{ $json.error_details || 'No details available' }}"}, {"name": "generationType", "value": "={{ $json.generationType }}"}]}, "options": {}}, "id": "error-response", "name": "Error Response", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1560, 400]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Extract Parameters", "type": "main", "index": 0}]]}, "Extract Parameters": {"main": [[{"node": "Generate Content Prompt", "type": "main", "index": 0}]]}, "Generate Content Prompt": {"main": [[{"node": "OpenAI Content Generation", "type": "main", "index": 0}]]}, "OpenAI Content Generation": {"main": [[{"node": "Process Generated Content", "type": "main", "index": 0}]]}, "Process Generated Content": {"main": [[{"node": "Generation Success Check", "type": "main", "index": 0}]]}, "Generation Success Check": {"main": [[{"node": "Save to Strapi", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}, "Save to Strapi": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1"}