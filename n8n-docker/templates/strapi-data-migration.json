{"name": "Strapi Data Migration", "nodes": [{"parameters": {}, "id": "manual-trigger", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "sourceEnvironment", "value": "development"}, {"name": "targetEnvironment", "value": "production"}, {"name": "contentType", "value": "articles"}, {"name": "batchSize", "value": "10"}, {"name": "dryRun", "value": "false"}]}, "options": {}}, "id": "migration-config", "name": "Migration Config", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "strapi<PERSON><PERSON>", "url": "={{ $env.STRAPI_DEV_URL }}/api/{{ $node['Migration Config'].json.contentType }}?pagination[pageSize]=100&populate=*", "options": {"response": {"response": {"responseFormat": "json"}}}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $env.STRAPI_DEV_TOKEN }}"}]}}, "id": "fetch-source-data", "name": "Fetch Source Data", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"jsCode": "// Process and validate data for migration\nconst sourceData = $input.all()[0].json.data;\nconst config = $node['Migration Config'].json;\nconst processedItems = [];\n\nfor (const item of sourceData) {\n  // Remove system fields that shouldn't be migrated\n  const { id, createdAt, updatedAt, publishedAt, ...cleanData } = item.attributes;\n  \n  // Validate required fields\n  if (!cleanData.title && !cleanData.name) {\n    console.log(`Skipping item without title/name: ${item.id}`);\n    continue;\n  }\n  \n  // Transform data if needed\n  const processedItem = {\n    data: cleanData,\n    sourceId: item.id,\n    migrationTimestamp: new Date().toISOString()\n  };\n  \n  processedItems.push(processedItem);\n}\n\nconsole.log(`Processed ${processedItems.length} items for migration`);\n\nreturn processedItems.map(item => ({ json: item }));"}, "id": "process-data", "name": "Process Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"batchSize": "={{ parseInt($node['Migration Config'].json.batchSize) }}", "options": {}}, "id": "batch-processor", "name": "Batch Processor", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [1120, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $node['Migration Config'].json.dryRun }}", "operation": "equal", "value2": "false"}]}}, "id": "dry-run-check", "name": "Dry Run Check", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "strapi<PERSON><PERSON>", "method": "POST", "url": "={{ $env.STRAPI_PROD_URL }}/api/{{ $node['Migration Config'].json.contentType }}", "options": {"response": {"response": {"responseFormat": "json"}}, "timeout": 30000}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json.data }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $env.STRAPI_PROD_TOKEN }}"}, {"name": "Content-Type", "value": "application/json"}]}}, "id": "migrate-item", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1560, 200]}, {"parameters": {"values": {"string": [{"name": "action", "value": "dry_run_simulation"}, {"name": "sourceId", "value": "={{ $json.sourceId }}"}, {"name": "data", "value": "={{ JSON.stringify($json.data) }}"}, {"name": "timestamp", "value": "={{ $json.migrationTimestamp }}"}]}, "options": {}}, "id": "dry-run-log", "name": "Dry Run Log", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1560, 400]}, {"parameters": {"jsCode": "// Log migration results\nconst results = $input.all();\nconst successful = results.filter(r => r.json.id || r.json.action === 'dry_run_simulation');\nconst failed = results.filter(r => !r.json.id && r.json.action !== 'dry_run_simulation');\n\nconst summary = {\n  totalProcessed: results.length,\n  successful: successful.length,\n  failed: failed.length,\n  timestamp: new Date().toISOString(),\n  contentType: $node['Migration Config'].json.contentType,\n  sourceEnvironment: $node['Migration Config'].json.sourceEnvironment,\n  targetEnvironment: $node['Migration Config'].json.targetEnvironment,\n  dryRun: $node['Migration Config'].json.dryRun === 'true'\n};\n\nconsole.log('Migration Summary:', JSON.stringify(summary, null, 2));\n\nif (failed.length > 0) {\n  console.log('Failed items:', failed.map(f => f.json));\n}\n\nreturn [{ json: summary }];"}, "id": "migration-summary", "name": "Migration Summary", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 300]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.failed > 0 }}", "operation": "equal", "value2": true}]}}, "id": "error-check", "name": "Error Check", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [2000, 300]}, {"parameters": {"message": "Migration completed with errors. Failed items: {{ $json.failed }}", "options": {}}, "id": "error-notification", "name": "Error Notification", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [2220, 200]}, {"parameters": {"message": "Migration completed successfully. Processed: {{ $json.totalProcessed }}, Successful: {{ $json.successful }}", "options": {}}, "id": "success-notification", "name": "Success Notification", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [2220, 400]}], "connections": {"Manual Trigger": {"main": [[{"node": "Migration Config", "type": "main", "index": 0}]]}, "Migration Config": {"main": [[{"node": "Fetch Source Data", "type": "main", "index": 0}]]}, "Fetch Source Data": {"main": [[{"node": "Process Data", "type": "main", "index": 0}]]}, "Process Data": {"main": [[{"node": "Batch Processor", "type": "main", "index": 0}]]}, "Batch Processor": {"main": [[{"node": "Dry Run Check", "type": "main", "index": 0}]]}, "Dry Run Check": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Dry Run Log", "type": "main", "index": 0}]]}, "Migrate Item": {"main": [[{"node": "Migration Summary", "type": "main", "index": 0}]]}, "Dry Run Log": {"main": [[{"node": "Migration Summary", "type": "main", "index": 0}]]}, "Migration Summary": {"main": [[{"node": "Error Check", "type": "main", "index": 0}]]}, "Error Check": {"main": [[{"node": "Error Notification", "type": "main", "index": 0}], [{"node": "Success Notification", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1"}