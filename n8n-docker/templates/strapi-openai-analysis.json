{"name": "Strapi Content Analysis with OpenAI", "nodes": [{"parameters": {"httpMethod": "POST", "path": "analyze-content", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "contentType", "value": "={{ $json.contentType || 'articles' }}"}, {"name": "contentId", "value": "={{ $json.contentId || '' }}"}, {"name": "analysisType", "value": "={{ $json.analysisType || 'sentiment' }}"}, {"name": "environment", "value": "={{ $json.environment || 'development' }}"}, {"name": "fields", "value": "={{ JSON.stringify($json.fields || ['title', 'content', 'description']) }}"}]}, "options": {}}, "id": "extract-parameters", "name": "Extract Parameters", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "strapi<PERSON><PERSON>", "url": "={{ $node['Extract Parameters'].json.environment === 'production' ? $env.STRAPI_PROD_URL : $env.STRAPI_DEV_URL }}/api/{{ $node['Extract Parameters'].json.contentType }}{{ $node['Extract Parameters'].json.contentId ? '/' + $node['Extract Parameters'].json.contentId : '' }}?populate=*", "options": {"response": {"response": {"responseFormat": "json"}}}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $node['Extract Parameters'].json.environment === 'production' ? $env.STRAPI_PROD_TOKEN : $env.STRAPI_DEV_TOKEN }}"}]}}, "id": "fetch-content", "name": "Fetch Content", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"jsCode": "// Prepare content for OpenAI analysis\nconst content = $input.first().json;\nconst config = $node['Extract Parameters'].json;\nconst fieldsToAnalyze = JSON.parse(config.fields);\n\nlet textToAnalyze = '';\nlet contentData = {};\n\n// Handle single item or array of items\nconst items = content.data ? (Array.isArray(content.data) ? content.data : [content.data]) : [];\n\nif (items.length === 0) {\n  throw new Error('No content found to analyze');\n}\n\nconst results = [];\n\nfor (const item of items) {\n  const attributes = item.attributes || item;\n  \n  // Extract specified fields\n  const extractedText = fieldsToAnalyze\n    .map(field => {\n      const value = attributes[field];\n      if (typeof value === 'string') {\n        return `${field}: ${value}`;\n      } else if (typeof value === 'object' && value !== null) {\n        return `${field}: ${JSON.stringify(value)}`;\n      }\n      return '';\n    })\n    .filter(text => text.length > 0)\n    .join('\\n\\n');\n  \n  if (extractedText.trim().length === 0) {\n    console.log(`Skipping item ${item.id} - no analyzable text found`);\n    continue;\n  }\n  \n  results.push({\n    json: {\n      itemId: item.id,\n      contentType: config.contentType,\n      analysisType: config.analysisType,\n      textToAnalyze: extractedText,\n      originalData: attributes\n    }\n  });\n}\n\nif (results.length === 0) {\n  throw new Error('No valid content found for analysis');\n}\n\nreturn results;"}, "id": "prepare-content", "name": "Prepare Content", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"jsCode": "// Generate analysis prompt based on analysis type\nconst analysisType = $json.analysisType;\nconst textToAnalyze = $json.textToAnalyze;\n\nlet systemPrompt = '';\nlet userPrompt = '';\n\nswitch (analysisType) {\n  case 'sentiment':\n    systemPrompt = 'You are an expert content analyst specializing in sentiment analysis. Analyze the provided content and return a JSON response with sentiment score (-1 to 1), confidence level (0 to 1), key emotions detected, and a brief explanation.';\n    userPrompt = `Analyze the sentiment of this content:\\n\\n${textToAnalyze}\\n\\nReturn your analysis in this JSON format:\\n{\\n  \"sentiment_score\": 0.5,\\n  \"confidence\": 0.8,\\n  \"primary_emotion\": \"positive\",\\n  \"emotions_detected\": [\"joy\", \"excitement\"],\\n  \"explanation\": \"Brief explanation of the analysis\"\\n}`;\n    break;\n    \n  case 'readability':\n    systemPrompt = 'You are an expert content analyst specializing in readability assessment. Analyze the provided content and return a JSON response with readability metrics, target audience assessment, and improvement suggestions.';\n    userPrompt = `Analyze the readability of this content:\\n\\n${textToAnalyze}\\n\\nReturn your analysis in this JSON format:\\n{\\n  \"readability_score\": 7.5,\\n  \"reading_level\": \"High School\",\\n  \"target_audience\": \"General Public\",\\n  \"complexity_factors\": [\"sentence length\", \"vocabulary\"],\\n  \"suggestions\": [\"Shorter sentences recommended\"]\\n}`;\n    break;\n    \n  case 'keywords':\n    systemPrompt = 'You are an expert content analyst specializing in keyword extraction and SEO analysis. Extract relevant keywords, assess content themes, and provide SEO recommendations.';\n    userPrompt = `Extract keywords and analyze SEO potential of this content:\\n\\n${textToAnalyze}\\n\\nReturn your analysis in this JSON format:\\n{\\n  \"primary_keywords\": [\"keyword1\", \"keyword2\"],\\n  \"secondary_keywords\": [\"keyword3\", \"keyword4\"],\\n  \"content_themes\": [\"theme1\", \"theme2\"],\\n  \"seo_score\": 7.5,\\n  \"recommendations\": [\"Add more specific keywords\"]\\n}`;\n    break;\n    \n  case 'quality':\n    systemPrompt = 'You are an expert content quality assessor. Evaluate content for accuracy, completeness, engagement, and overall quality. Provide actionable improvement recommendations.';\n    userPrompt = `Assess the quality of this content:\\n\\n${textToAnalyze}\\n\\nReturn your analysis in this JSON format:\\n{\\n  \"quality_score\": 8.2,\\n  \"accuracy\": 9.0,\\n  \"completeness\": 7.5,\\n  \"engagement\": 8.0,\\n  \"strengths\": [\"Clear structure\", \"Good examples\"],\\n  \"weaknesses\": [\"Missing conclusion\"],\\n  \"recommendations\": [\"Add summary section\"]\\n}`;\n    break;\n    \n  default:\n    systemPrompt = 'You are an expert content analyst. Provide a comprehensive analysis of the given content including key insights, themes, and recommendations.';\n    userPrompt = `Analyze this content and provide insights:\\n\\n${textToAnalyze}\\n\\nReturn your analysis in JSON format with relevant metrics and recommendations.`;\n}\n\nreturn [{\n  json: {\n    ...($json),\n    systemPrompt,\n    userPrompt\n  }\n}];"}, "id": "generate-prompt", "name": "Generate Analysis Prompt", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi", "method": "POST", "url": "https://api.openai.com/v1/chat/completions", "options": {"response": {"response": {"responseFormat": "json"}}, "timeout": 60000}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"gpt-4\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"{{ $json.systemPrompt }}\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"{{ $json.userPrompt }}\"\n    }\n  ],\n  \"temperature\": 0.3,\n  \"max_tokens\": 1000,\n  \"response_format\": { \"type\": \"json_object\" }\n}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $env.OPENAI_API_KEY }}"}, {"name": "Content-Type", "value": "application/json"}]}}, "id": "openai-analysis", "name": "OpenAI Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1340, 300]}, {"parameters": {"jsCode": "// Process OpenAI response and prepare final result\nconst openaiResponse = $input.first().json;\nconst originalData = $node['Generate Analysis Prompt'].json;\n\ntry {\n  const analysisResult = JSON.parse(openaiResponse.choices[0].message.content);\n  \n  const finalResult = {\n    itemId: originalData.itemId,\n    contentType: originalData.contentType,\n    analysisType: originalData.analysisType,\n    timestamp: new Date().toISOString(),\n    analysis: analysisResult,\n    metadata: {\n      model: 'gpt-4',\n      tokens_used: openaiResponse.usage?.total_tokens || 0,\n      processing_time: new Date().toISOString()\n    }\n  };\n  \n  return [{ json: finalResult }];\n  \n} catch (error) {\n  console.error('Error parsing OpenAI response:', error);\n  \n  return [{\n    json: {\n      itemId: originalData.itemId,\n      contentType: originalData.contentType,\n      analysisType: originalData.analysisType,\n      timestamp: new Date().toISOString(),\n      error: 'Failed to parse analysis result',\n      raw_response: openaiResponse.choices[0]?.message?.content || 'No response content'\n    }\n  }];\n}"}, "id": "process-result", "name": "Process Analysis Result", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.error }}", "operation": "isEmpty"}]}}, "id": "success-check", "name": "Success Check", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1780, 300]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "strapi<PERSON><PERSON>", "method": "POST", "url": "={{ $node['Extract Parameters'].json.environment === 'production' ? $env.STRAPI_PROD_URL : $env.STRAPI_DEV_URL }}/api/content-analyses", "options": {"response": {"response": {"responseFormat": "json"}}}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"data\": {\n    \"content_id\": {{ $json.itemId }},\n    \"content_type\": \"{{ $json.contentType }}\",\n    \"analysis_type\": \"{{ $json.analysisType }}\",\n    \"analysis_result\": {{ JSON.stringify($json.analysis) }},\n    \"metadata\": {{ JSON.stringify($json.metadata) }},\n    \"analyzed_at\": \"{{ $json.timestamp }}\"\n  }\n}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $node['Extract Parameters'].json.environment === 'production' ? $env.STRAPI_PROD_TOKEN : $env.STRAPI_DEV_TOKEN }}"}, {"name": "Content-Type", "value": "application/json"}]}}, "id": "save-analysis", "name": "Save Analysis to Strapi", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2000, 200]}, {"parameters": {"values": {"string": [{"name": "status", "value": "success"}, {"name": "message", "value": "Content analysis completed and saved"}]}, "options": {}}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [2220, 200]}, {"parameters": {"values": {"string": [{"name": "status", "value": "error"}, {"name": "message", "value": "Analysis failed"}, {"name": "error_details", "value": "={{ $json.error || 'Unknown error' }}"}]}, "options": {}}, "id": "error-response", "name": "Error Response", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [2000, 400]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Extract Parameters", "type": "main", "index": 0}]]}, "Extract Parameters": {"main": [[{"node": "Fetch Content", "type": "main", "index": 0}]]}, "Fetch Content": {"main": [[{"node": "Prepare Content", "type": "main", "index": 0}]]}, "Prepare Content": {"main": [[{"node": "Generate Analysis Prompt", "type": "main", "index": 0}]]}, "Generate Analysis Prompt": {"main": [[{"node": "OpenAI Analysis", "type": "main", "index": 0}]]}, "OpenAI Analysis": {"main": [[{"node": "Process Analysis Result", "type": "main", "index": 0}]]}, "Process Analysis Result": {"main": [[{"node": "Success Check", "type": "main", "index": 0}]]}, "Success Check": {"main": [[{"node": "Save Analysis to Strapi", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}, "Save Analysis to Strapi": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1"}