{"name": "Strapi MCP Client", "nodes": [{"parameters": {"httpMethod": "POST", "path": "mcp-client", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "operation", "value": "={{ $json.operation || 'get_content' }}"}, {"name": "environment", "value": "={{ $json.environment || 'development' }}"}, {"name": "contentType", "value": "={{ $json.contentType || 'articles' }}"}, {"name": "contentId", "value": "={{ $json.contentId || '' }}"}, {"name": "data", "value": "={{ JSON.stringify($json.data || {}) }}"}, {"name": "filters", "value": "={{ JSON.stringify($json.filters || {}) }}"}, {"name": "mcpServerUrl", "value": "={{ $env.MCP_SERVER_URL || 'http://localhost:3001' }}"}]}, "options": {}}, "id": "extract-parameters", "name": "Extract Parameters", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"method": "POST", "url": "={{ $node['Extract Parameters'].json.mcpServerUrl }}/auth/token", "options": {"response": {"response": {"responseFormat": "json"}}}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"apiKey\": \"{{ $node['Extract Parameters'].json.environment === 'production' ? $env.MCP_PROD_API_KEY : $env.MCP_DEV_API_KEY }}\",\n  \"environment\": \"{{ $node['Extract Parameters'].json.environment }}\"\n}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "id": "authenticate-mcp", "name": "Authenticate with MCP", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"jsCode": "// Prepare MCP tool call based on operation\nconst operation = $node['Extract Parameters'].json.operation;\nconst params = $node['Extract Parameters'].json;\n\nlet toolName;\nlet toolArguments = {};\n\nswitch (operation) {\n  case 'get_content':\n    toolName = 'strapi_get_content';\n    toolArguments = {\n      contentType: params.contentType,\n      id: params.contentId || undefined,\n      filters: params.filters ? JSON.parse(params.filters) : undefined,\n      populate: '*'\n    };\n    break;\n    \n  case 'create_content':\n    toolName = 'strapi_create_content';\n    toolArguments = {\n      contentType: params.contentType,\n      data: JSON.parse(params.data)\n    };\n    break;\n    \n  case 'update_content':\n    toolName = 'strapi_update_content';\n    toolArguments = {\n      contentType: params.contentType,\n      id: params.contentId,\n      data: JSON.parse(params.data)\n    };\n    break;\n    \n  case 'delete_content':\n    toolName = 'strapi_delete_content';\n    toolArguments = {\n      contentType: params.contentType,\n      id: params.contentId\n    };\n    break;\n    \n  case 'search_content':\n    toolName = 'strapi_search_content';\n    toolArguments = {\n      contentType: params.contentType,\n      query: params.query || '',\n      fields: params.searchFields ? JSON.parse(params.searchFields) : undefined,\n      limit: params.limit || 25\n    };\n    break;\n    \n  case 'get_schema':\n    toolName = 'strapi_get_schema';\n    toolArguments = {\n      contentType: params.contentType\n    };\n    break;\n    \n  case 'bulk_operation':\n    toolName = 'strapi_bulk_operation';\n    toolArguments = {\n      operation: params.bulkOperation || 'create',\n      contentType: params.contentType,\n      items: JSON.parse(params.data),\n      batchSize: params.batchSize || 10\n    };\n    break;\n    \n  default:\n    throw new Error(`Unknown operation: ${operation}`);\n}\n\n// Remove undefined values\nObject.keys(toolArguments).forEach(key => {\n  if (toolArguments[key] === undefined) {\n    delete toolArguments[key];\n  }\n});\n\nreturn [{\n  json: {\n    toolName,\n    toolArguments,\n    operation,\n    mcpServerUrl: params.mcpServerUrl\n  }\n}];"}, "id": "prepare-tool-call", "name": "Prepare Tool Call", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"method": "POST", "url": "={{ $node['Prepare Tool Call'].json.mcpServerUrl }}/mcp/tools/call", "options": {"response": {"response": {"responseFormat": "json"}}, "timeout": 60000}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"name\": \"{{ $node['Prepare Tool Call'].json.toolName }}\",\n  \"arguments\": {{ JSON.stringify($node['Prepare Tool Call'].json.toolArguments) }}\n}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $node['Authenticate with MCP'].json.token }}"}, {"name": "Content-Type", "value": "application/json"}]}}, "id": "call-mcp-tool", "name": "Call MCP Tool", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 300]}, {"parameters": {"jsCode": "// Process MCP response\nconst mcpResponse = $input.first().json;\nconst operation = $node['Prepare Tool Call'].json.operation;\n\nif (mcpResponse.isError) {\n  throw new Error(`MCP Tool Error: ${mcpResponse.content[0]?.text || 'Unknown error'}`);\n}\n\nlet result;\ntry {\n  // Parse the JSON response from MCP tool\n  const responseText = mcpResponse.content[0]?.text;\n  if (responseText) {\n    result = JSON.parse(responseText);\n  } else {\n    throw new Error('No response content from MCP tool');\n  }\n} catch (parseError) {\n  throw new Error(`Failed to parse MCP response: ${parseError.message}`);\n}\n\n// Format response based on operation type\nconst formattedResponse = {\n  operation,\n  success: true,\n  timestamp: new Date().toISOString(),\n  data: result\n};\n\n// Add operation-specific metadata\nswitch (operation) {\n  case 'get_content':\n    formattedResponse.count = Array.isArray(result.data) ? result.data.length : (result.data ? 1 : 0);\n    break;\n    \n  case 'create_content':\n  case 'update_content':\n    formattedResponse.contentId = result.data?.id;\n    break;\n    \n  case 'bulk_operation':\n    formattedResponse.summary = {\n      total: result.totalItems,\n      successful: result.successful,\n      failed: result.failed\n    };\n    break;\n    \n  case 'search_content':\n    formattedResponse.count = result.data?.length || 0;\n    formattedResponse.pagination = result.meta?.pagination;\n    break;\n}\n\nreturn [{ json: formattedResponse }];"}, "id": "process-response", "name": "Process MCP Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 300]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.success }}", "operation": "equal", "value2": true}]}}, "id": "success-check", "name": "Success Check", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1560, 300]}, {"parameters": {"values": {"string": [{"name": "status", "value": "success"}, {"name": "operation", "value": "={{ $node['Process MCP Response'].json.operation }}"}, {"name": "timestamp", "value": "={{ $node['Process MCP Response'].json.timestamp }}"}, {"name": "message", "value": "MCP operation completed successfully"}]}, "options": {}}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1780, 200]}, {"parameters": {"values": {"string": [{"name": "status", "value": "error"}, {"name": "operation", "value": "={{ $node['Process MCP Response'].json.operation || 'unknown' }}"}, {"name": "timestamp", "value": "={{ new Date().toISOString() }}"}, {"name": "error", "value": "MCP operation failed"}]}, "options": {}}, "id": "error-response", "name": "Error Response", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1780, 400]}, {"parameters": {"jsCode": "// Log operation for monitoring and debugging\nconst response = $input.first().json;\nconst operation = response.operation || 'unknown';\nconst status = response.status;\n\nconst logEntry = {\n  timestamp: new Date().toISOString(),\n  operation,\n  status,\n  environment: $node['Extract Parameters'].json.environment,\n  contentType: $node['Extract Parameters'].json.contentType,\n  duration: Date.now() - new Date($node['Process MCP Response'].json.timestamp).getTime(),\n  success: status === 'success'\n};\n\n// Add operation-specific metrics\nif (response.count !== undefined) {\n  logEntry.itemCount = response.count;\n}\n\nif (response.summary) {\n  logEntry.summary = response.summary;\n}\n\nif (response.contentId) {\n  logEntry.contentId = response.contentId;\n}\n\nconsole.log('MCP Operation Log:', JSON.stringify(logEntry, null, 2));\n\nreturn [{ json: { ...response, log: logEntry } }];"}, "id": "log-operation", "name": "Log Operation", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2000, 300]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Extract Parameters", "type": "main", "index": 0}]]}, "Extract Parameters": {"main": [[{"node": "Authenticate with MCP", "type": "main", "index": 0}]]}, "Authenticate with MCP": {"main": [[{"node": "Prepare Tool Call", "type": "main", "index": 0}]]}, "Prepare Tool Call": {"main": [[{"node": "Call MCP Tool", "type": "main", "index": 0}]]}, "Call MCP Tool": {"main": [[{"node": "Process MCP Response", "type": "main", "index": 0}]]}, "Process MCP Response": {"main": [[{"node": "Success Check", "type": "main", "index": 0}]]}, "Success Check": {"main": [[{"node": "Success Response", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}, "Success Response": {"main": [[{"node": "Log Operation", "type": "main", "index": 0}]]}, "Error Response": {"main": [[{"node": "Log Operation", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1"}