{"name": "Strapi CRUD Enhanced", "nodes": [{"parameters": {}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "strapi-crud-webhook"}, {"parameters": {"values": {"string": [{"name": "operation", "value": "={{ $json.operation || 'read' }}"}, {"name": "contentType", "value": "={{ $json.contentType || 'articles' }}"}, {"name": "id", "value": "={{ $json.id || '' }}"}, {"name": "data", "value": "={{ JSON.stringify($json.data || {}) }}"}, {"name": "environment", "value": "={{ $json.environment || 'development' }}"}]}, "options": {}}, "id": "extract-parameters", "name": "Extract Parameters", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $node['Extract Parameters'].json.operation }}", "operation": "equal", "value2": "create"}]}}, "id": "operation-router", "name": "Operation Router", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "strapi<PERSON><PERSON>", "url": "={{ $node['Extract Parameters'].json.environment === 'production' ? $env.STRAPI_PROD_URL : $env.STRAPI_DEV_URL }}/api/{{ $node['Extract Parameters'].json.contentType }}", "options": {"response": {"response": {"responseFormat": "json"}}}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.parse($node['Extract Parameters'].json.data) }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $node['Extract Parameters'].json.environment === 'production' ? $env.STRAPI_PROD_TOKEN : $env.STRAPI_DEV_TOKEN }}"}, {"name": "Content-Type", "value": "application/json"}]}}, "id": "create-operation", "name": "Create Operation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 200]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $node['Extract Parameters'].json.operation }}", "operation": "equal", "value2": "read"}]}}, "id": "read-router", "name": "Read Router", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "strapi<PERSON><PERSON>", "url": "={{ $node['Extract Parameters'].json.environment === 'production' ? $env.STRAPI_PROD_URL : $env.STRAPI_DEV_URL }}/api/{{ $node['Extract Parameters'].json.contentType }}{{ $node['Extract Parameters'].json.id ? '/' + $node['Extract Parameters'].json.id : '' }}", "options": {"response": {"response": {"responseFormat": "json"}}}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $node['Extract Parameters'].json.environment === 'production' ? $env.STRAPI_PROD_TOKEN : $env.STRAPI_DEV_TOKEN }}"}]}}, "id": "read-operation", "name": "Read Operation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $node['Extract Parameters'].json.operation }}", "operation": "equal", "value2": "update"}]}}, "id": "update-router", "name": "Update Router", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [900, 600]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "strapi<PERSON><PERSON>", "method": "PUT", "url": "={{ $node['Extract Parameters'].json.environment === 'production' ? $env.STRAPI_PROD_URL : $env.STRAPI_DEV_URL }}/api/{{ $node['Extract Parameters'].json.contentType }}/{{ $node['Extract Parameters'].json.id }}", "options": {"response": {"response": {"responseFormat": "json"}}}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.parse($node['Extract Parameters'].json.data) }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $node['Extract Parameters'].json.environment === 'production' ? $env.STRAPI_PROD_TOKEN : $env.STRAPI_DEV_TOKEN }}"}, {"name": "Content-Type", "value": "application/json"}]}}, "id": "update-operation", "name": "Update Operation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 500]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $node['Extract Parameters'].json.operation }}", "operation": "equal", "value2": "delete"}]}}, "id": "delete-router", "name": "Delete Router", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [900, 800]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "strapi<PERSON><PERSON>", "method": "DELETE", "url": "={{ $node['Extract Parameters'].json.environment === 'production' ? $env.STRAPI_PROD_URL : $env.STRAPI_DEV_URL }}/api/{{ $node['Extract Parameters'].json.contentType }}/{{ $node['Extract Parameters'].json.id }}", "options": {"response": {"response": {"responseFormat": "json"}}}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $node['Extract Parameters'].json.environment === 'production' ? $env.STRAPI_PROD_TOKEN : $env.STRAPI_DEV_TOKEN }}"}]}}, "id": "delete-operation", "name": "Delete Operation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 700]}, {"parameters": {"values": {"string": [{"name": "status", "value": "success"}, {"name": "operation", "value": "={{ $node['Extract Parameters'].json.operation }}"}, {"name": "contentType", "value": "={{ $node['Extract Parameters'].json.contentType }}"}, {"name": "environment", "value": "={{ $node['Extract Parameters'].json.environment }}"}, {"name": "timestamp", "value": "={{ new Date().toISOString() }}"}]}, "options": {}}, "id": "format-response", "name": "Format Response", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1340, 400]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Extract Parameters", "type": "main", "index": 0}]]}, "Extract Parameters": {"main": [[{"node": "Operation Router", "type": "main", "index": 0}]]}, "Operation Router": {"main": [[{"node": "Create Operation", "type": "main", "index": 0}], [{"node": "Read Router", "type": "main", "index": 0}]]}, "Create Operation": {"main": [[{"node": "Format Response", "type": "main", "index": 0}]]}, "Read Router": {"main": [[{"node": "Read Operation", "type": "main", "index": 0}], [{"node": "Update Router", "type": "main", "index": 0}]]}, "Read Operation": {"main": [[{"node": "Format Response", "type": "main", "index": 0}]]}, "Update Router": {"main": [[{"node": "Update Operation", "type": "main", "index": 0}], [{"node": "Delete Router", "type": "main", "index": 0}]]}, "Update Operation": {"main": [[{"node": "Format Response", "type": "main", "index": 0}]]}, "Delete Router": {"main": [[{"node": "Delete Operation", "type": "main", "index": 0}]]}, "Delete Operation": {"main": [[{"node": "Format Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1"}