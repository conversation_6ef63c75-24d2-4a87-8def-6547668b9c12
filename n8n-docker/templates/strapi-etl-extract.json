{"name": "Strapi ETL - Extract", "nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 2 * * *"}]}}, "id": "schedule-trigger", "name": "Schedule Trigger", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "sourceEnvironment", "value": "development"}, {"name": "contentTypes", "value": "articles,pages,products,categories"}, {"name": "extractMode", "value": "incremental"}, {"name": "lastExtractTime", "value": "={{ $env.LAST_EXTRACT_TIME || new Date(Date.now() - 24*60*60*1000).toISOString() }}"}, {"name": "batchSize", "value": "100"}, {"name": "outputFormat", "value": "json"}]}, "options": {}}, "id": "extract-config", "name": "Extract Configuration", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"jsCode": "// Parse content types and prepare extraction jobs\nconst config = $node['Extract Configuration'].json;\nconst contentTypes = config.contentTypes.split(',').map(type => type.trim());\nconst extractJobs = [];\n\nfor (const contentType of contentTypes) {\n  extractJobs.push({\n    json: {\n      contentType,\n      sourceEnvironment: config.sourceEnvironment,\n      extractMode: config.extractMode,\n      lastExtractTime: config.lastExtractTime,\n      batchSize: parseInt(config.batchSize),\n      outputFormat: config.outputFormat,\n      jobId: `extract_${contentType}_${Date.now()}`\n    }\n  });\n}\n\nconsole.log(`Created ${extractJobs.length} extraction jobs`);\nreturn extractJobs;"}, "id": "prepare-extraction-jobs", "name": "Prepare Extraction Jobs", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"jsCode": "// Build extraction query based on mode\nconst job = $json;\nconst extractMode = job.extractMode;\nconst lastExtractTime = job.lastExtractTime;\n\nlet filters = {};\nlet populate = '*';\nlet sort = 'createdAt:asc';\n\n// Configure extraction based on mode\nswitch (extractMode) {\n  case 'incremental':\n    // Extract only items modified since last extraction\n    filters = {\n      $or: [\n        { createdAt: { $gt: lastExtractTime } },\n        { updatedAt: { $gt: lastExtractTime } }\n      ]\n    };\n    sort = 'updatedAt:asc';\n    break;\n    \n  case 'full':\n    // Extract all items\n    filters = {};\n    break;\n    \n  case 'published_only':\n    // Extract only published items\n    filters = {\n      publishedAt: { $notNull: true }\n    };\n    break;\n    \n  case 'recent':\n    // Extract items from last 7 days\n    const sevenDaysAgo = new Date(Date.now() - 7*24*60*60*1000).toISOString();\n    filters = {\n      createdAt: { $gt: sevenDaysAgo }\n    };\n    break;\n    \n  default:\n    filters = {};\n}\n\nreturn [{\n  json: {\n    ...job,\n    extractQuery: {\n      filters,\n      populate,\n      sort,\n      pagination: {\n        pageSize: job.batchSize,\n        page: 1\n      }\n    }\n  }\n}];"}, "id": "build-extraction-query", "name": "Build Extraction Query", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "strapi<PERSON><PERSON>", "url": "={{ $env.STRAPI_DEV_URL }}/api/{{ $json.contentType }}", "options": {"response": {"response": {"responseFormat": "json"}}, "timeout": 60000}, "sendQuery": true, "specifyQuery": "json", "jsonQuery": "={{ JSON.stringify($json.extractQuery) }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $env.STRAPI_DEV_TOKEN }}"}]}}, "id": "extract-first-page", "name": "Extract First Page", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 300]}, {"parameters": {"jsCode": "// Process first page and determine if pagination is needed\nconst response = $input.first().json;\nconst job = $node['Build Extraction Query'].json;\n\nconst extractedData = response.data || [];\nconst pagination = response.meta?.pagination;\nconst totalPages = pagination?.pageCount || 1;\nconst currentPage = pagination?.page || 1;\n\nconst result = {\n  jobId: job.jobId,\n  contentType: job.contentType,\n  extractMode: job.extractMode,\n  currentPage,\n  totalPages,\n  extractedItems: extractedData.length,\n  totalItems: pagination?.total || extractedData.length,\n  data: extractedData,\n  needsPagination: totalPages > 1,\n  extractQuery: job.extractQuery,\n  timestamp: new Date().toISOString()\n};\n\nconsole.log(`Extracted page 1/${totalPages} for ${job.contentType}: ${extractedData.length} items`);\n\nreturn [{ json: result }];"}, "id": "process-first-page", "name": "Process First Page", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 300]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.needsPagination }}", "operation": "equal", "value2": true}]}}, "id": "pagination-check", "name": "Pagination Check", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1560, 300]}, {"parameters": {"jsCode": "// Generate pagination jobs for remaining pages\nconst firstPageResult = $json;\nconst totalPages = firstPageResult.totalPages;\nconst extractQuery = firstPageResult.extractQuery;\n\nconst paginationJobs = [];\n\n// Create jobs for pages 2 through totalPages\nfor (let page = 2; page <= totalPages; page++) {\n  const pageQuery = {\n    ...extractQuery,\n    pagination: {\n      ...extractQuery.pagination,\n      page\n    }\n  };\n  \n  paginationJobs.push({\n    json: {\n      jobId: firstPageResult.jobId,\n      contentType: firstPageResult.contentType,\n      page,\n      totalPages,\n      extractQuery: pageQuery,\n      isPageJob: true\n    }\n  });\n}\n\nconsole.log(`Created ${paginationJobs.length} pagination jobs for ${firstPageResult.contentType}`);\nreturn paginationJobs;"}, "id": "generate-pagination-jobs", "name": "Generate Pagination Jobs", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 200]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "strapi<PERSON><PERSON>", "url": "={{ $env.STRAPI_DEV_URL }}/api/{{ $json.contentType }}", "options": {"response": {"response": {"responseFormat": "json"}}, "timeout": 60000}, "sendQuery": true, "specifyQuery": "json", "jsonQuery": "={{ JSON.stringify($json.extractQuery) }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $env.STRAPI_DEV_TOKEN }}"}]}}, "id": "extract-additional-pages", "name": "Extract Additional Pages", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2000, 200]}, {"parameters": {"jsCode": "// Process additional page data\nconst response = $input.first().json;\nconst job = $node['Generate Pagination Jobs'].json;\n\nconst extractedData = response.data || [];\n\nconst result = {\n  jobId: job.jobId,\n  contentType: job.contentType,\n  page: job.page,\n  totalPages: job.totalPages,\n  extractedItems: extractedData.length,\n  data: extractedData,\n  isPageJob: true,\n  timestamp: new Date().toISOString()\n};\n\nconsole.log(`Extracted page ${job.page}/${job.totalPages} for ${job.contentType}: ${extractedData.length} items`);\n\nreturn [{ json: result }];"}, "id": "process-additional-pages", "name": "Process Additional Pages", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2220, 200]}, {"parameters": {"jsCode": "// Aggregate all extracted data\nconst allResults = $input.all();\nconst firstPageResult = allResults.find(r => !r.json.isPageJob);\nconst pageResults = allResults.filter(r => r.json.isPageJob);\n\nif (!firstPageResult) {\n  throw new Error('First page result not found');\n}\n\n// Combine all data\nlet allData = [...firstPageResult.json.data];\nlet totalExtracted = firstPageResult.json.extractedItems;\n\nfor (const pageResult of pageResults) {\n  allData = allData.concat(pageResult.json.data);\n  totalExtracted += pageResult.json.extractedItems;\n}\n\n// Transform data for output\nconst transformedData = allData.map(item => {\n  const transformed = {\n    id: item.id,\n    attributes: item.attributes,\n    meta: {\n      extractedAt: new Date().toISOString(),\n      sourceEnvironment: firstPageResult.json.extractMode,\n      contentType: firstPageResult.json.contentType\n    }\n  };\n  \n  return transformed;\n});\n\nconst finalResult = {\n  jobId: firstPageResult.json.jobId,\n  contentType: firstPageResult.json.contentType,\n  extractMode: firstPageResult.json.extractMode,\n  totalExtracted,\n  totalPages: firstPageResult.json.totalPages,\n  data: transformedData,\n  extractedAt: new Date().toISOString(),\n  summary: {\n    contentType: firstPageResult.json.contentType,\n    itemCount: totalExtracted,\n    pagesProcessed: firstPageResult.json.totalPages,\n    extractMode: firstPageResult.json.extractMode\n  }\n};\n\nconsole.log(`Extraction completed for ${firstPageResult.json.contentType}: ${totalExtracted} items from ${firstPageResult.json.totalPages} pages`);\n\nreturn [{ json: finalResult }];"}, "id": "aggregate-extracted-data", "name": "Aggregate Extracted Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 400]}, {"parameters": {"jsCode": "// Save extracted data to file or database\nconst extractResult = $json;\nconst outputFormat = 'json'; // Fixed format for consistency\nconst timestamp = new Date().toISOString().replace(/[:.]/g, '-');\nconst filename = `extract_${extractResult.contentType}_${timestamp}.${outputFormat}`;\n\n// Prepare data for storage\nconst outputData = JSON.stringify({\n  metadata: {\n    jobId: extractResult.jobId,\n    contentType: extractResult.contentType,\n    extractMode: extractResult.extractMode,\n    extractedAt: extractResult.extractedAt,\n    totalItems: extractResult.totalExtracted\n  },\n  data: extractResult.data\n}, null, 2);\n\nreturn [{\n  json: {\n    ...extractResult,\n    output: {\n      filename,\n      format: outputFormat,\n      size: outputData.length,\n      data: outputData\n    }\n  }\n}];"}, "id": "prepare-output", "name": "Prepare Output", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2000, 400]}, {"parameters": {"values": {"string": [{"name": "status", "value": "completed"}, {"name": "jobId", "value": "={{ $json.jobId }}"}, {"name": "contentType", "value": "={{ $json.contentType }}"}, {"name": "totalExtracted", "value": "={{ $json.totalExtracted }}"}, {"name": "filename", "value": "={{ $json.output.filename }}"}, {"name": "extractedAt", "value": "={{ $json.extractedAt }}"}]}, "options": {}}, "id": "extraction-summary", "name": "Extraction Summary", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [2220, 400]}], "connections": {"Schedule Trigger": {"main": [[{"node": "Extract Configuration", "type": "main", "index": 0}]]}, "Extract Configuration": {"main": [[{"node": "Prepare Extraction Jobs", "type": "main", "index": 0}]]}, "Prepare Extraction Jobs": {"main": [[{"node": "Build Extraction Query", "type": "main", "index": 0}]]}, "Build Extraction Query": {"main": [[{"node": "Extract First Page", "type": "main", "index": 0}]]}, "Extract First Page": {"main": [[{"node": "Process First Page", "type": "main", "index": 0}]]}, "Process First Page": {"main": [[{"node": "Pagination Check", "type": "main", "index": 0}]]}, "Pagination Check": {"main": [[{"node": "Generate Pagination Jobs", "type": "main", "index": 0}], [{"node": "Aggregate Extracted Data", "type": "main", "index": 0}]]}, "Generate Pagination Jobs": {"main": [[{"node": "Extract Additional Pages", "type": "main", "index": 0}]]}, "Extract Additional Pages": {"main": [[{"node": "Process Additional Pages", "type": "main", "index": 0}]]}, "Process Additional Pages": {"main": [[{"node": "Aggregate Extracted Data", "type": "main", "index": 0}]]}, "Aggregate Extracted Data": {"main": [[{"node": "Prepare Output", "type": "main", "index": 0}]]}, "Prepare Output": {"main": [[{"node": "Extraction Summary", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1"}