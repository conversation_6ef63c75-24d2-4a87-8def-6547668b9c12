{"name": "Strapi ETL - Load", "nodes": [{"parameters": {"httpMethod": "POST", "path": "load-data", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "sourceFile", "value": "={{ $json.sourceFile || '' }}"}, {"name": "targetEnvironment", "value": "={{ $json.targetEnvironment || 'production' }}"}, {"name": "contentType", "value": "={{ $json.contentType || 'articles' }}"}, {"name": "loadMode", "value": "={{ $json.loadMode || 'insert' }}"}, {"name": "batchSize", "value": "={{ $json.batchSize || '10' }}"}, {"name": "conflictResolution", "value": "={{ $json.conflictResolution || 'skip' }}"}, {"name": "dryRun", "value": "={{ $json.dryRun || 'false' }}"}]}, "options": {}}, "id": "extract-parameters", "name": "Extract Parameters", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"jsCode": "// Load and validate source data\nconst params = $node['Extract Parameters'].json;\nconst sourceFile = params.sourceFile;\n\n// Load data from webhook payload or simulate file loading\nlet sourceData;\n\nif ($input.first().json.data) {\n  // Data provided in webhook payload\n  sourceData = $input.first().json.data;\n} else if (sourceFile) {\n  // Simulate loading from file\n  console.log(`Loading data from: ${sourceFile}`);\n  // In real implementation: sourceData = loadFromFile(sourceFile);\n  \n  // For demo, use sample data\n  sourceData = {\n    metadata: {\n      transformationType: 'field_mapping',\n      targetContentType: params.contentType,\n      totalItems: 2\n    },\n    data: [\n      {\n        id: 'temp_1',\n        attributes: {\n          title: 'Sample Article 1',\n          content: 'This is sample content for article 1',\n          slug: 'sample-article-1',\n          publishedAt: new Date().toISOString()\n        },\n        meta: {\n          transformedAt: new Date().toISOString()\n        }\n      },\n      {\n        id: 'temp_2',\n        attributes: {\n          title: 'Sample Article 2',\n          content: 'This is sample content for article 2',\n          slug: 'sample-article-2',\n          publishedAt: new Date().toISOString()\n        },\n        meta: {\n          transformedAt: new Date().toISOString()\n        }\n      }\n    ]\n  };\n} else {\n  throw new Error('No source data provided');\n}\n\n// Validate data structure\nif (!sourceData.data || !Array.isArray(sourceData.data)) {\n  throw new Error('Invalid source data format');\n}\n\nconst result = {\n  sourceData: sourceData.data,\n  metadata: sourceData.metadata || {},\n  totalItems: sourceData.data.length,\n  targetEnvironment: params.targetEnvironment,\n  contentType: params.contentType,\n  loadMode: params.loadMode,\n  batchSize: parseInt(params.batchSize),\n  conflictResolution: params.conflictResolution,\n  dryRun: params.dryRun === 'true',\n  loadedAt: new Date().toISOString()\n};\n\nconsole.log(`Loaded ${result.totalItems} items for loading to ${params.targetEnvironment}`);\nreturn [{ json: result }];"}, "id": "load-source-data", "name": "Load Source Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"jsCode": "// Prepare items for batch processing\nconst { sourceData, batchSize, loadMode, conflictResolution, dryRun } = $json;\nconst batches = [];\n\n// Split data into batches\nfor (let i = 0; i < sourceData.length; i += batchSize) {\n  const batch = sourceData.slice(i, i + batchSize);\n  \n  batches.push({\n    json: {\n      batchNumber: Math.floor(i / batchSize) + 1,\n      totalBatches: Math.ceil(sourceData.length / batchSize),\n      batchSize: batch.length,\n      items: batch,\n      loadMode,\n      conflictResolution,\n      dryRun,\n      targetEnvironment: $json.targetEnvironment,\n      contentType: $json.contentType\n    }\n  });\n}\n\nconsole.log(`Created ${batches.length} batches for loading`);\nreturn batches;"}, "id": "prepare-batches", "name": "Prepare <PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"jsCode": "// Process batch based on load mode\nconst batch = $json;\nconst { items, loadMode, conflictResolution, dryRun, targetEnvironment, contentType } = batch;\n\nconst results = [];\nconst errors = [];\nconst skipped = [];\n\nfor (let i = 0; i < items.length; i++) {\n  const item = items[i];\n  \n  try {\n    let result;\n    \n    if (dryRun) {\n      // Simulate the operation\n      result = {\n        action: 'dry_run_simulation',\n        item: item,\n        wouldPerform: loadMode,\n        conflictResolution: conflictResolution\n      };\n    } else {\n      // Prepare for actual API call\n      switch (loadMode) {\n        case 'insert':\n          result = {\n            action: 'create',\n            data: item.attributes,\n            originalId: item.id\n          };\n          break;\n          \n        case 'upsert':\n          // Check if item exists first (simplified)\n          result = {\n            action: 'upsert',\n            data: item.attributes,\n            originalId: item.id,\n            checkField: 'slug' // or another unique field\n          };\n          break;\n          \n        case 'update':\n          if (!item.id || item.id.startsWith('temp_')) {\n            throw new Error('Update mode requires existing item ID');\n          }\n          result = {\n            action: 'update',\n            id: item.id,\n            data: item.attributes\n          };\n          break;\n          \n        default:\n          throw new Error(`Unknown load mode: ${loadMode}`);\n      }\n    }\n    \n    results.push({\n      index: i,\n      originalItem: item,\n      result: result,\n      status: 'prepared'\n    });\n    \n  } catch (error) {\n    errors.push({\n      index: i,\n      originalItem: item,\n      error: error.message,\n      status: 'error'\n    });\n  }\n}\n\nconst batchResult = {\n  batchNumber: batch.batchNumber,\n  totalBatches: batch.totalBatches,\n  processedItems: results.length,\n  errorItems: errors.length,\n  results,\n  errors,\n  skipped,\n  targetEnvironment,\n  contentType,\n  dryRun,\n  processedAt: new Date().toISOString()\n};\n\nconsole.log(`Batch ${batch.batchNumber}/${batch.totalBatches} processed: ${results.length} prepared, ${errors.length} errors`);\nreturn [{ json: batchResult }];"}, "id": "process-batch", "name": "Process Batch", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.dryRun }}", "operation": "equal", "value2": false}]}}, "id": "dry-run-check", "name": "Dry Run Check", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"jsCode": "// Execute actual API calls for non-dry-run mode\nconst batchResult = $json;\nconst { results, targetEnvironment, contentType } = batchResult;\n\nconst apiResults = [];\nconst apiErrors = [];\n\n// Get Strapi URL and token based on environment\nconst strapiUrl = targetEnvironment === 'production' \n  ? process.env.STRAPI_PROD_URL \n  : process.env.STRAPI_DEV_URL;\n  \nconst strapiToken = targetEnvironment === 'production'\n  ? process.env.STRAPI_PROD_TOKEN\n  : process.env.STRAPI_DEV_TOKEN;\n\nif (!strapiUrl || !strapiToken) {\n  throw new Error(`Strapi configuration missing for environment: ${targetEnvironment}`);\n}\n\n// Process each prepared result\nfor (const preparedResult of results) {\n  const { result, originalItem, index } = preparedResult;\n  \n  try {\n    let apiCall;\n    \n    switch (result.action) {\n      case 'create':\n        apiCall = {\n          method: 'POST',\n          url: `${strapiUrl}/api/${contentType}`,\n          data: { data: result.data },\n          headers: {\n            'Authorization': `Bearer ${strapiToken}`,\n            'Content-Type': 'application/json'\n          }\n        };\n        break;\n        \n      case 'update':\n        apiCall = {\n          method: 'PUT',\n          url: `${strapiUrl}/api/${contentType}/${result.id}`,\n          data: { data: result.data },\n          headers: {\n            'Authorization': `Bearer ${strapiToken}`,\n            'Content-Type': 'application/json'\n          }\n        };\n        break;\n        \n      case 'upsert':\n        // For upsert, we need to check if item exists first\n        // This is a simplified version - in practice, you'd make a GET request first\n        apiCall = {\n          method: 'POST',\n          url: `${strapiUrl}/api/${contentType}`,\n          data: { data: result.data },\n          headers: {\n            'Authorization': `Bearer ${strapiToken}`,\n            'Content-Type': 'application/json'\n          },\n          isUpsert: true\n        };\n        break;\n        \n      default:\n        throw new Error(`Unknown action: ${result.action}`);\n    }\n    \n    // Store API call for execution\n    apiResults.push({\n      index,\n      originalItem,\n      apiCall,\n      action: result.action,\n      status: 'ready_for_execution'\n    });\n    \n  } catch (error) {\n    apiErrors.push({\n      index,\n      originalItem,\n      error: error.message,\n      action: result.action,\n      status: 'preparation_failed'\n    });\n  }\n}\n\nconst executionBatch = {\n  ...batchResult,\n  apiResults,\n  apiErrors,\n  readyForExecution: apiResults.length,\n  preparationErrors: apiErrors.length,\n  preparedAt: new Date().toISOString()\n};\n\nconsole.log(`API calls prepared: ${apiResults.length} ready, ${apiErrors.length} preparation errors`);\nreturn [{ json: executionBatch }];"}, "id": "prepare-api-calls", "name": "Prepare API Calls", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 200]}, {"parameters": {"jsCode": "// Execute API calls with error handling and retry logic\nconst executionBatch = $json;\nconst { apiResults } = executionBatch;\n\nconst executionResults = [];\nconst executionErrors = [];\n\n// Simulate API execution (in real implementation, use HTTP Request nodes)\nfor (const apiResult of apiResults) {\n  const { apiCall, originalItem, action, index } = apiResult;\n  \n  try {\n    // Simulate API call execution\n    // In real implementation, this would be actual HTTP requests\n    const simulatedResponse = {\n      data: {\n        id: Math.floor(Math.random() * 10000) + 1000, // Simulate generated ID\n        attributes: apiCall.data.data,\n        meta: {\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString()\n        }\n      },\n      status: 200\n    };\n    \n    executionResults.push({\n      index,\n      originalItem,\n      action,\n      response: simulatedResponse,\n      createdId: simulatedResponse.data.id,\n      status: 'success',\n      executedAt: new Date().toISOString()\n    });\n    \n  } catch (error) {\n    executionErrors.push({\n      index,\n      originalItem,\n      action,\n      error: error.message,\n      status: 'execution_failed',\n      executedAt: new Date().toISOString()\n    });\n  }\n}\n\nconst finalBatchResult = {\n  ...executionBatch,\n  executionResults,\n  executionErrors,\n  successfulExecutions: executionResults.length,\n  failedExecutions: executionErrors.length,\n  executedAt: new Date().toISOString()\n};\n\nconsole.log(`API execution completed: ${executionResults.length} successful, ${executionErrors.length} failed`);\nreturn [{ json: finalBatchResult }];"}, "id": "execute-api-calls", "name": "Execute API Calls", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1780, 200]}, {"parameters": {"jsCode": "// Handle dry run results\nconst batchResult = $json;\nconst { results } = batchResult;\n\nconst dryRunResults = results.map(result => ({\n  ...result,\n  status: 'dry_run_completed',\n  simulatedAt: new Date().toISOString()\n}));\n\nconst dryRunBatch = {\n  ...batchResult,\n  dryRunResults,\n  simulatedOperations: dryRunResults.length,\n  completedAt: new Date().toISOString()\n};\n\nconsole.log(`Dry run completed: ${dryRunResults.length} operations simulated`);\nreturn [{ json: dryRunBatch }];"}, "id": "handle-dry-run", "name": "Handle Dry Run", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 400]}, {"parameters": {"jsCode": "// Aggregate results from all batches\nconst allBatches = $input.all();\nconst isDryRun = allBatches[0]?.json.dryRun || false;\n\nlet totalProcessed = 0;\nlet totalSuccessful = 0;\nlet totalFailed = 0;\nlet totalSkipped = 0;\n\nconst aggregatedResults = {\n  batches: [],\n  summary: {},\n  errors: [],\n  successful: []\n};\n\nfor (const batch of allBatches) {\n  const batchData = batch.json;\n  \n  aggregatedResults.batches.push({\n    batchNumber: batchData.batchNumber,\n    processedItems: batchData.processedItems || 0,\n    successfulExecutions: batchData.successfulExecutions || 0,\n    failedExecutions: batchData.failedExecutions || 0,\n    simulatedOperations: batchData.simulatedOperations || 0\n  });\n  \n  totalProcessed += batchData.processedItems || 0;\n  \n  if (isDryRun) {\n    totalSuccessful += batchData.simulatedOperations || 0;\n  } else {\n    totalSuccessful += batchData.successfulExecutions || 0;\n    totalFailed += batchData.failedExecutions || 0;\n    \n    // Collect successful results\n    if (batchData.executionResults) {\n      aggregatedResults.successful.push(...batchData.executionResults);\n    }\n    \n    // Collect errors\n    if (batchData.executionErrors) {\n      aggregatedResults.errors.push(...batchData.executionErrors);\n    }\n  }\n  \n  // Collect preparation errors\n  if (batchData.errors) {\n    aggregatedResults.errors.push(...batchData.errors);\n  }\n}\n\naggregatedResults.summary = {\n  isDryRun,\n  totalBatches: allBatches.length,\n  totalProcessed,\n  totalSuccessful,\n  totalFailed,\n  totalSkipped,\n  successRate: totalProcessed > 0 ? (totalSuccessful / totalProcessed * 100).toFixed(2) : 0,\n  targetEnvironment: allBatches[0]?.json.targetEnvironment,\n  contentType: allBatches[0]?.json.contentType,\n  completedAt: new Date().toISOString()\n};\n\nconst logMessage = isDryRun \n  ? `Dry run completed: ${totalSuccessful} operations simulated`\n  : `Load completed: ${totalSuccessful} successful, ${totalFailed} failed`;\n  \nconsole.log(logMessage);\nconsole.log('Load Summary:', JSON.stringify(aggregatedResults.summary, null, 2));\n\nreturn [{ json: aggregatedResults }];"}, "id": "aggregate-results", "name": "Aggregate Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2000, 300]}, {"parameters": {"values": {"string": [{"name": "status", "value": "={{ $json.summary.totalFailed > 0 ? 'completed_with_errors' : 'completed_successfully' }}"}, {"name": "is<PERSON>ry<PERSON>un", "value": "={{ $json.summary.isDryRun }}"}, {"name": "totalProcessed", "value": "={{ $json.summary.totalProcessed }}"}, {"name": "totalSuccessful", "value": "={{ $json.summary.totalSuccessful }}"}, {"name": "totalFailed", "value": "={{ $json.summary.totalFailed }}"}, {"name": "successRate", "value": "={{ $json.summary.successRate }}%"}, {"name": "targetEnvironment", "value": "={{ $json.summary.targetEnvironment }}"}, {"name": "contentType", "value": "={{ $json.summary.contentType }}"}, {"name": "completedAt", "value": "={{ $json.summary.completedAt }}"}]}, "options": {}}, "id": "load-summary", "name": "<PERSON><PERSON>mmary", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [2220, 300]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Extract Parameters", "type": "main", "index": 0}]]}, "Extract Parameters": {"main": [[{"node": "Load Source Data", "type": "main", "index": 0}]]}, "Load Source Data": {"main": [[{"node": "Prepare <PERSON>", "type": "main", "index": 0}]]}, "Prepare Batches": {"main": [[{"node": "Process Batch", "type": "main", "index": 0}]]}, "Process Batch": {"main": [[{"node": "Dry Run Check", "type": "main", "index": 0}]]}, "Dry Run Check": {"main": [[{"node": "Prepare API Calls", "type": "main", "index": 0}], [{"node": "Handle Dry Run", "type": "main", "index": 0}]]}, "Prepare API Calls": {"main": [[{"node": "Execute API Calls", "type": "main", "index": 0}]]}, "Execute API Calls": {"main": [[{"node": "Aggregate Results", "type": "main", "index": 0}]]}, "Handle Dry Run": {"main": [[{"node": "Aggregate Results", "type": "main", "index": 0}]]}, "Aggregate Results": {"main": [[{"node": "<PERSON><PERSON>mmary", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1"}