{"name": "Strapi Content Translation with OpenAI", "nodes": [{"parameters": {"httpMethod": "POST", "path": "translate-content", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "contentType", "value": "={{ $json.contentType || 'articles' }}"}, {"name": "contentId", "value": "={{ $json.contentId || '' }}"}, {"name": "sourceLocale", "value": "={{ $json.sourceLocale || 'en' }}"}, {"name": "targetLocales", "value": "={{ JSON.stringify($json.targetLocales || ['es', 'fr']) }}"}, {"name": "fieldsToTranslate", "value": "={{ JSON.stringify($json.fieldsToTranslate || ['title', 'content', 'description']) }}"}, {"name": "environment", "value": "={{ $json.environment || 'development' }}"}, {"name": "preserveFormatting", "value": "={{ $json.preserveFormatting || 'true' }}"}]}, "options": {}}, "id": "extract-parameters", "name": "Extract Parameters", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "strapi<PERSON><PERSON>", "url": "={{ $node['Extract Parameters'].json.environment === 'production' ? $env.STRAPI_PROD_URL : $env.STRAPI_DEV_URL }}/api/{{ $node['Extract Parameters'].json.contentType }}/{{ $node['Extract Parameters'].json.contentId }}?populate=*&locale={{ $node['Extract Parameters'].json.sourceLocale }}", "options": {"response": {"response": {"responseFormat": "json"}}}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $node['Extract Parameters'].json.environment === 'production' ? $env.STRAPI_PROD_TOKEN : $env.STRAPI_DEV_TOKEN }}"}]}}, "id": "fetch-source-content", "name": "Fetch Source Content", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"jsCode": "// Prepare content for translation\nconst sourceContent = $input.first().json.data;\nconst config = $node['Extract Parameters'].json;\nconst fieldsToTranslate = JSON.parse(config.fieldsToTranslate);\nconst targetLocales = JSON.parse(config.targetLocales);\n\nif (!sourceContent || !sourceContent.attributes) {\n  throw new Error('Source content not found');\n}\n\nconst sourceAttributes = sourceContent.attributes;\nconst translationJobs = [];\n\n// Create translation jobs for each target locale\nfor (const targetLocale of targetLocales) {\n  const contentToTranslate = {};\n  \n  // Extract fields that need translation\n  for (const field of fieldsToTranslate) {\n    if (sourceAttributes[field]) {\n      contentToTranslate[field] = sourceAttributes[field];\n    }\n  }\n  \n  if (Object.keys(contentToTranslate).length > 0) {\n    translationJobs.push({\n      json: {\n        sourceId: sourceContent.id,\n        sourceLocale: config.sourceLocale,\n        targetLocale: targetLocale,\n        contentType: config.contentType,\n        fieldsToTranslate: contentToTranslate,\n        preserveFormatting: config.preserveFormatting === 'true',\n        originalAttributes: sourceAttributes\n      }\n    });\n  }\n}\n\nif (translationJobs.length === 0) {\n  throw new Error('No content found to translate');\n}\n\nconsole.log(`Created ${translationJobs.length} translation jobs`);\nreturn translationJobs;"}, "id": "prepare-translation-jobs", "name": "Prepare Translation Jobs", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"jsCode": "// Generate translation prompt\nconst job = $json;\nconst fieldsToTranslate = job.fieldsToTranslate;\nconst sourceLocale = job.sourceLocale;\nconst targetLocale = job.targetLocale;\n\n// Language mapping for better prompts\nconst languageNames = {\n  'en': 'English',\n  'es': 'Spanish',\n  'fr': 'French',\n  'de': 'German',\n  'it': 'Italian',\n  'pt': 'Portuguese',\n  'ru': 'Russian',\n  'ja': 'Japanese',\n  'ko': 'Korean',\n  'zh': 'Chinese',\n  'ar': 'Arabic'\n};\n\nconst sourceLang = languageNames[sourceLocale] || sourceLocale;\nconst targetLang = languageNames[targetLocale] || targetLocale;\n\nconst systemPrompt = `You are a professional translator specializing in content localization. Translate the provided content from ${sourceLang} to ${targetLang}. \n\nGuidelines:\n- Maintain the original meaning and tone\n- Preserve formatting (HTML tags, markdown, etc.) if present\n- Adapt cultural references appropriately\n- Keep technical terms consistent\n- Return the translation in the exact same JSON structure as provided\n- Only translate the text values, not the field names`;\n\nconst contentJson = JSON.stringify(fieldsToTranslate, null, 2);\n\nconst userPrompt = `Translate this content from ${sourceLang} to ${targetLang}:\n\n${contentJson}\n\nReturn the translated content in the same JSON structure, translating only the text values while preserving all formatting and structure.`;\n\nreturn [{\n  json: {\n    ...job,\n    systemPrompt,\n    userPrompt,\n    sourceLang,\n    targetLang\n  }\n}];"}, "id": "generate-translation-prompt", "name": "Generate Translation Prompt", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi", "method": "POST", "url": "https://api.openai.com/v1/chat/completions", "options": {"response": {"response": {"responseFormat": "json"}}, "timeout": 120000}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"gpt-4\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"{{ $json.systemPrompt }}\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"{{ $json.userPrompt }}\"\n    }\n  ],\n  \"temperature\": 0.3,\n  \"max_tokens\": 2000,\n  \"response_format\": { \"type\": \"json_object\" }\n}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $env.OPENAI_API_KEY }}"}, {"name": "Content-Type", "value": "application/json"}]}}, "id": "openai-translation", "name": "OpenAI Translation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1340, 300]}, {"parameters": {"jsCode": "// Process translation result\nconst openaiResponse = $input.first().json;\nconst jobData = $node['Generate Translation Prompt'].json;\n\ntry {\n  const translatedContent = JSON.parse(openaiResponse.choices[0].message.content);\n  \n  // Merge translated fields with original attributes\n  const updatedAttributes = { ...jobData.originalAttributes };\n  \n  // Update only the translated fields\n  Object.keys(translatedContent).forEach(field => {\n    updatedAttributes[field] = translatedContent[field];\n  });\n  \n  // Set locale for the translated content\n  updatedAttributes.locale = jobData.targetLocale;\n  \n  const result = {\n    sourceId: jobData.sourceId,\n    targetLocale: jobData.targetLocale,\n    contentType: jobData.contentType,\n    translatedAttributes: updatedAttributes,\n    translatedFields: translatedContent,\n    metadata: {\n      sourceLang: jobData.sourceLang,\n      targetLang: jobData.targetLang,\n      model: 'gpt-4',\n      tokens_used: openaiResponse.usage?.total_tokens || 0,\n      translated_at: new Date().toISOString()\n    },\n    success: true\n  };\n  \n  return [{ json: result }];\n  \n} catch (error) {\n  console.error('Translation processing error:', error);\n  \n  return [{\n    json: {\n      sourceId: jobData.sourceId,\n      targetLocale: jobData.targetLocale,\n      contentType: jobData.contentType,\n      success: false,\n      error: 'Failed to process translation',\n      error_details: error.message,\n      raw_response: openaiResponse.choices[0]?.message?.content || 'No response'\n    }\n  }];\n}"}, "id": "process-translation", "name": "Process Translation", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 300]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.success }}", "operation": "equal", "value2": true}]}}, "id": "translation-success-check", "name": "Translation Success Check", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1780, 300]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "strapi<PERSON><PERSON>", "method": "POST", "url": "={{ $node['Extract Parameters'].json.environment === 'production' ? $env.STRAPI_PROD_URL : $env.STRAPI_DEV_URL }}/api/{{ $json.contentType }}", "options": {"response": {"response": {"responseFormat": "json"}}}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"data\": {{ JSON.stringify($json.translatedAttributes) }}\n}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $node['Extract Parameters'].json.environment === 'production' ? $env.STRAPI_PROD_TOKEN : $env.STRAPI_DEV_TOKEN }}"}, {"name": "Content-Type", "value": "application/json"}]}}, "id": "create-translated-content", "name": "Create Translated Content", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2000, 200]}, {"parameters": {"values": {"string": [{"name": "status", "value": "success"}, {"name": "sourceId", "value": "={{ $node['Process Translation'].json.sourceId }}"}, {"name": "translatedId", "value": "={{ $json.data.id }}"}, {"name": "targetLocale", "value": "={{ $node['Process Translation'].json.targetLocale }}"}, {"name": "contentType", "value": "={{ $node['Process Translation'].json.contentType }}"}]}, "options": {}}, "id": "success-result", "name": "Success Result", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [2220, 200]}, {"parameters": {"values": {"string": [{"name": "status", "value": "error"}, {"name": "sourceId", "value": "={{ $json.sourceId }}"}, {"name": "targetLocale", "value": "={{ $json.targetLocale }}"}, {"name": "error", "value": "={{ $json.error || 'Translation failed' }}"}, {"name": "error_details", "value": "={{ $json.error_details || 'Unknown error' }}"}]}, "options": {}}, "id": "error-result", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [2000, 400]}, {"parameters": {"jsCode": "// Aggregate all translation results\nconst results = $input.all();\nconst successful = results.filter(r => r.json.status === 'success');\nconst failed = results.filter(r => r.json.status === 'error');\n\nconst summary = {\n  total_translations: results.length,\n  successful: successful.length,\n  failed: failed.length,\n  source_content_id: results[0]?.json.sourceId,\n  content_type: results[0]?.json.contentType,\n  completed_locales: successful.map(r => r.json.targetLocale),\n  failed_locales: failed.map(r => r.json.targetLocale),\n  timestamp: new Date().toISOString()\n};\n\nif (failed.length > 0) {\n  summary.errors = failed.map(f => ({\n    locale: f.json.targetLocale,\n    error: f.json.error,\n    details: f.json.error_details\n  }));\n}\n\nif (successful.length > 0) {\n  summary.created_translations = successful.map(s => ({\n    locale: s.json.targetLocale,\n    translated_id: s.json.translatedId\n  }));\n}\n\nconsole.log('Translation Summary:', JSON.stringify(summary, null, 2));\n\nreturn [{ json: summary }];"}, "id": "aggregate-results", "name": "Aggregate Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2440, 300]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Extract Parameters", "type": "main", "index": 0}]]}, "Extract Parameters": {"main": [[{"node": "Fetch Source Content", "type": "main", "index": 0}]]}, "Fetch Source Content": {"main": [[{"node": "Prepare Translation Jobs", "type": "main", "index": 0}]]}, "Prepare Translation Jobs": {"main": [[{"node": "Generate Translation Prompt", "type": "main", "index": 0}]]}, "Generate Translation Prompt": {"main": [[{"node": "OpenAI Translation", "type": "main", "index": 0}]]}, "OpenAI Translation": {"main": [[{"node": "Process Translation", "type": "main", "index": 0}]]}, "Process Translation": {"main": [[{"node": "Translation Success Check", "type": "main", "index": 0}]]}, "Translation Success Check": {"main": [[{"node": "Create Translated Content", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Create Translated Content": {"main": [[{"node": "Success Result", "type": "main", "index": 0}]]}, "Success Result": {"main": [[{"node": "Aggregate Results", "type": "main", "index": 0}]]}, "Error Result": {"main": [[{"node": "Aggregate Results", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1"}