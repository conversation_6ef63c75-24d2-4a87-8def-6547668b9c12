# Deployment Guide

This guide covers deploying the n8n Strapi Automation Suite to production environments.

## 🏗 Production Deployment

### Prerequisites

- <PERSON>er and Docker Compose (production versions)
- SSL certificates for HTTPS
- Domain names configured
- Production Strapi instances
- OpenAI API account with sufficient credits
- Monitoring and logging infrastructure

### 1. Production Environment Setup

#### Environment Configuration

Create a production `.env` file:

```bash
# Production Environment Variables
NODE_ENV=production

# n8n Configuration
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=your_secure_production_password
N8N_SECURE_COOKIE=true
N8N_HOST=your-n8n-domain.com
N8N_PROTOCOL=https
N8N_PORT=443

# Database Configuration
DB_TYPE=postgresdb
DB_POSTGRESDB_HOST=your-postgres-host
DB_POSTGRESDB_PORT=5432
DB_POSTGRESDB_DATABASE=n8n_production
DB_POSTGRESDB_USER=n8n_prod
DB_POSTGRESDB_PASSWORD=your_secure_db_password

# Strapi Configuration
STRAPI_DEV_URL=https://dev-strapi.yourdomain.com
STRAPI_DEV_TOKEN=your_dev_token
STRAPI_PROD_URL=https://strapi.yourdomain.com
STRAPI_PROD_TOKEN=your_prod_token

# OpenAI Configuration
OPENAI_API_KEY=sk-your_production_openai_key
OPENAI_ORG_ID=org-your_organization_id

# MCP Server Configuration
MCP_SERVER_HOST=0.0.0.0
MCP_SERVER_PORT=3001
JWT_SECRET=your_production_jwt_secret_32_chars
MCP_DEV_API_KEY=your_secure_dev_api_key
MCP_PROD_API_KEY=your_secure_prod_api_key

# Security
ENCRYPTION_KEY=your_32_character_encryption_key_here
WEBHOOK_SECRET=your_webhook_secret_here

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MAX_REQUESTS=1000
RATE_LIMIT_WINDOW_MS=3600000

# Monitoring
LOG_LEVEL=info
ENABLE_METRICS=true
METRICS_PORT=9090

# Email Configuration
SMTP_HOST=smtp.yourdomain.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password
SMTP_FROM=<EMAIL>

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_REGION=us-east-1
BACKUP_S3_ACCESS_KEY=your_s3_access_key
BACKUP_S3_SECRET_KEY=your_s3_secret_key
```

### 2. Production Docker Compose

Create `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  n8n:
    image: n8nio/n8n:latest
    ports:
      - "80:5678"
      - "443:5678"
    environment:
      - NODE_ENV=production
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=${N8N_BASIC_AUTH_USER}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_BASIC_AUTH_PASSWORD}
      - N8N_SECURE_COOKIE=true
      - N8N_HOST=${N8N_HOST}
      - N8N_PROTOCOL=https
      - N8N_PORT=443
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=${DB_POSTGRESDB_HOST}
      - DB_POSTGRESDB_PORT=${DB_POSTGRESDB_PORT}
      - DB_POSTGRESDB_DATABASE=${DB_POSTGRESDB_DATABASE}
      - DB_POSTGRESDB_USER=${DB_POSTGRESDB_USER}
      - DB_POSTGRESDB_PASSWORD=${DB_POSTGRESDB_PASSWORD}
      - NODE_FUNCTION_ALLOW_EXTERNAL=axios,qs,lodash,moment
      - GENERIC_TIMEZONE=UTC
    depends_on:
      - db
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./ssl:/etc/ssl/certs:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  db:
    image: postgres:14-alpine
    environment:
      - POSTGRES_USER=${DB_POSTGRESDB_USER}
      - POSTGRES_PASSWORD=${DB_POSTGRESDB_PASSWORD}
      - POSTGRES_DB=${DB_POSTGRESDB_DATABASE}
    volumes:
      - db_data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_POSTGRESDB_USER}"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  mcp-server:
    build:
      context: ./mcp-server
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - MCP_SERVER_HOST=${MCP_SERVER_HOST}
      - MCP_SERVER_PORT=${MCP_SERVER_PORT}
      - LOG_LEVEL=${LOG_LEVEL}
      - STRAPI_DEV_URL=${STRAPI_DEV_URL}
      - STRAPI_DEV_TOKEN=${STRAPI_DEV_TOKEN}
      - STRAPI_PROD_URL=${STRAPI_PROD_URL}
      - STRAPI_PROD_TOKEN=${STRAPI_PROD_TOKEN}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - JWT_SECRET=${JWT_SECRET}
      - RATE_LIMIT_ENABLED=${RATE_LIMIT_ENABLED}
      - RATE_LIMIT_MAX_REQUESTS=${RATE_LIMIT_MAX_REQUESTS}
      - RATE_LIMIT_WINDOW_MS=${RATE_LIMIT_WINDOW_MS}
    volumes:
      - mcp_logs:/app/logs
    restart: unless-stopped
    depends_on:
      - n8n
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/ssl/certs:ro
    depends_on:
      - n8n
      - mcp-server
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  n8n_data:
  db_data:
  mcp_logs:
  redis_data:
```

### 3. Nginx Configuration

Create `nginx.conf`:

```nginx
events {
    worker_connections 1024;
}

http {
    upstream n8n {
        server n8n:5678;
    }

    upstream mcp-server {
        server mcp-server:3001;
    }

    server {
        listen 80;
        server_name your-n8n-domain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-n8n-domain.com;

        ssl_certificate /etc/ssl/certs/your-domain.crt;
        ssl_certificate_key /etc/ssl/certs/your-domain.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;

        location / {
            proxy_pass http://n8n;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket support
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        location /mcp/ {
            proxy_pass http://mcp-server/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

### 4. SSL Certificate Setup

#### Using Let's Encrypt (Recommended)

```bash
# Install certbot
sudo apt-get update
sudo apt-get install certbot

# Generate certificates
sudo certbot certonly --standalone -d your-n8n-domain.com

# Copy certificates
sudo cp /etc/letsencrypt/live/your-n8n-domain.com/fullchain.pem ./ssl/your-domain.crt
sudo cp /etc/letsencrypt/live/your-n8n-domain.com/privkey.pem ./ssl/your-domain.key

# Set proper permissions
sudo chown $USER:$USER ./ssl/*
chmod 600 ./ssl/*
```

### 5. Deployment Commands

```bash
# Deploy to production
docker-compose -f docker-compose.prod.yml up -d

# Check service status
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f

# Update services
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d
```

## 🔒 Security Hardening

### 1. Network Security

```bash
# Configure firewall
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw deny 5678/tcp
sudo ufw deny 3001/tcp
sudo ufw enable
```

### 2. Container Security

- Use non-root users in containers
- Implement resource limits
- Regular security updates
- Vulnerability scanning

### 3. API Security

- Strong authentication tokens
- Rate limiting
- Input validation
- HTTPS only
- CORS configuration

## 📊 Monitoring and Alerting

### 1. Health Monitoring

Create `monitoring/docker-compose.yml`:

```yaml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=your_grafana_password
    volumes:
      - grafana_data:/var/lib/grafana

volumes:
  prometheus_data:
  grafana_data:
```

### 2. Log Aggregation

- Use ELK stack or similar
- Centralized logging
- Log rotation
- Alert on errors

### 3. Performance Monitoring

- Application metrics
- Database performance
- API response times
- Resource utilization

## 🔄 Backup and Recovery

### 1. Database Backup

```bash
# Create backup script
#!/bin/bash
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="n8n_production"

docker exec postgres_container pg_dump -U n8n_prod $DB_NAME > $BACKUP_DIR/n8n_backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/n8n_backup_$DATE.sql

# Upload to S3
aws s3 cp $BACKUP_DIR/n8n_backup_$DATE.sql.gz s3://your-backup-bucket/
```

### 2. Workflow Backup

```bash
# Export all workflows
docker exec n8n_container n8n export:workflow --all --output=/backups/workflows_$DATE.json
```

### 3. Recovery Procedures

- Database restoration
- Workflow import
- Configuration recovery
- Service restart procedures

## 🚀 Scaling and Performance

### 1. Horizontal Scaling

- Load balancer configuration
- Multiple n8n instances
- Database clustering
- Redis clustering

### 2. Performance Optimization

- Resource allocation
- Database tuning
- Caching strategies
- CDN integration

### 3. Auto-scaling

- Container orchestration (Kubernetes)
- Auto-scaling policies
- Resource monitoring
- Cost optimization

## 📋 Maintenance Procedures

### 1. Regular Updates

```bash
# Update containers
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d

# Update workflows
# Import new templates via n8n UI
```

### 2. Health Checks

- Service availability
- Database connectivity
- API endpoints
- SSL certificate expiry

### 3. Performance Reviews

- Workflow execution times
- Resource utilization
- Error rates
- User feedback

---

This deployment guide ensures a robust, secure, and scalable production environment for your n8n Strapi automation suite.
