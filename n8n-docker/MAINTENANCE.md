# Maintenance and Troubleshooting Guide

This guide covers routine maintenance tasks and troubleshooting procedures for the n8n Strapi Automation Suite.

## 🔧 Routine Maintenance

### Daily Tasks

#### 1. Health Check Monitoring
```bash
# Check service status
docker-compose ps

# Verify health endpoints
curl -f http://localhost:5678/healthz
curl -f http://localhost:3001/health

# Check logs for errors
docker-compose logs --tail=100 n8n | grep -i error
docker-compose logs --tail=100 mcp-server | grep -i error
```

#### 2. Workflow Execution Review
- Monitor failed workflow executions in n8n dashboard
- Review execution times for performance issues
- Check webhook endpoint availability
- Verify API rate limits and quotas

### Weekly Tasks

#### 1. Database Maintenance
```bash
# Check database size and performance
docker exec -it postgres_container psql -U n8n -d n8n -c "
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation 
FROM pg_stats 
WHERE schemaname = 'public';"

# Analyze and vacuum database
docker exec -it postgres_container psql -U n8n -d n8n -c "VACUUM ANALYZE;"
```

#### 2. Log Rotation and Cleanup
```bash
# Clean old logs
docker system prune -f
docker volume prune -f

# Rotate application logs
find /var/log -name "*.log" -type f -mtime +7 -delete
```

#### 3. Backup Verification
```bash
# Test backup restoration
./scripts/test-backup-restore.sh

# Verify S3 backup uploads
aws s3 ls s3://your-backup-bucket/ --recursive | tail -10
```

### Monthly Tasks

#### 1. Security Updates
```bash
# Update base images
docker-compose pull
docker-compose up -d

# Update MCP server dependencies
cd mcp-server
npm audit fix
npm update
```

#### 2. Performance Review
- Analyze workflow execution metrics
- Review resource utilization trends
- Optimize slow-performing workflows
- Update rate limits based on usage patterns

#### 3. Capacity Planning
- Monitor storage growth
- Review API usage trends
- Plan for scaling requirements
- Update backup retention policies

## 🚨 Troubleshooting Guide

### Common Issues and Solutions

#### 1. n8n Service Issues

**Problem**: n8n container won't start
```bash
# Check logs
docker-compose logs n8n

# Common solutions:
# - Check environment variables
# - Verify database connectivity
# - Check port conflicts
# - Validate SSL certificates
```

**Problem**: Workflows fail to execute
```bash
# Check workflow logs in n8n UI
# Verify credentials are valid
# Test API endpoints manually
curl -H "Authorization: Bearer $STRAPI_TOKEN" $STRAPI_URL/api/articles
```

**Problem**: Database connection errors
```bash
# Check database status
docker-compose exec db pg_isready -U n8n

# Reset database connection
docker-compose restart n8n
```

#### 2. MCP Server Issues

**Problem**: MCP server authentication failures
```bash
# Check JWT secret configuration
echo $JWT_SECRET | wc -c  # Should be 32+ characters

# Verify API keys
curl -X POST http://localhost:3001/auth/token \
  -H "Content-Type: application/json" \
  -d '{"apiKey": "your-api-key", "environment": "development"}'
```

**Problem**: MCP server high memory usage
```bash
# Check memory usage
docker stats mcp-server

# Restart with memory limit
docker-compose up -d --scale mcp-server=0
docker-compose up -d --scale mcp-server=1
```

#### 3. Strapi Integration Issues

**Problem**: Strapi API timeouts
```bash
# Test Strapi connectivity
curl -w "@curl-format.txt" -o /dev/null -s $STRAPI_URL/api/articles

# Check Strapi server logs
# Increase timeout in workflows
# Implement retry logic
```

**Problem**: Authentication token expired
```bash
# Generate new token in Strapi admin
# Update environment variables
# Restart affected services
docker-compose restart n8n mcp-server
```

#### 4. OpenAI Integration Issues

**Problem**: OpenAI rate limit exceeded
```bash
# Check current usage
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
  https://api.openai.com/v1/usage

# Solutions:
# - Implement exponential backoff
# - Reduce request frequency
# - Upgrade OpenAI plan
# - Cache responses
```

**Problem**: OpenAI API errors
```bash
# Test API connectivity
curl -X POST https://api.openai.com/v1/chat/completions \
  -H "Authorization: Bearer $OPENAI_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"model": "gpt-3.5-turbo", "messages": [{"role": "user", "content": "test"}]}'

# Check API key validity
# Verify model availability
# Review request format
```

### Performance Issues

#### 1. Slow Workflow Execution

**Diagnosis**:
```bash
# Check system resources
docker stats

# Monitor database performance
docker exec -it postgres_container psql -U n8n -d n8n -c "
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;"
```

**Solutions**:
- Optimize database queries
- Increase container resources
- Implement caching
- Use batch processing
- Optimize workflow logic

#### 2. High Memory Usage

**Diagnosis**:
```bash
# Check memory usage by service
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

# Check for memory leaks
docker exec -it n8n_container node -e "console.log(process.memoryUsage())"
```

**Solutions**:
- Restart services periodically
- Optimize data processing
- Implement pagination
- Increase memory limits
- Review workflow complexity

#### 3. Network Connectivity Issues

**Diagnosis**:
```bash
# Test internal network connectivity
docker exec -it n8n_container ping mcp-server
docker exec -it mcp-server ping db

# Check external connectivity
docker exec -it n8n_container curl -I https://api.openai.com
```

**Solutions**:
- Check firewall rules
- Verify DNS resolution
- Update network configuration
- Check proxy settings

## 🔍 Monitoring and Alerting

### Key Metrics to Monitor

#### 1. System Metrics
- CPU utilization
- Memory usage
- Disk space
- Network I/O

#### 2. Application Metrics
- Workflow execution success rate
- API response times
- Error rates
- Queue lengths

#### 3. Business Metrics
- Content processing volume
- Translation requests
- API usage costs
- User activity

### Alert Configuration

#### 1. Critical Alerts
```yaml
# Example Prometheus alert rules
groups:
  - name: n8n-critical
    rules:
      - alert: N8NServiceDown
        expr: up{job="n8n"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "n8n service is down"

      - alert: DatabaseConnectionFailed
        expr: up{job="postgres"} == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Database connection failed"
```

#### 2. Warning Alerts
- High error rates (>5%)
- Slow response times (>30s)
- High resource usage (>80%)
- Failed backups

### Log Analysis

#### 1. Error Pattern Detection
```bash
# Find common error patterns
docker-compose logs n8n | grep -i error | sort | uniq -c | sort -nr

# Analyze workflow failures
docker-compose logs n8n | grep "Workflow execution failed" | tail -20
```

#### 2. Performance Analysis
```bash
# Find slow operations
docker-compose logs mcp-server | grep "duration" | sort -k3 -nr | head -10

# Analyze API response times
docker-compose logs nginx | awk '{print $10}' | sort -nr | head -20
```

## 🛠 Maintenance Scripts

### 1. Health Check Script
```bash
#!/bin/bash
# health-check.sh

echo "=== n8n Strapi Automation Suite Health Check ==="

# Check services
services=("n8n" "db" "mcp-server")
for service in "${services[@]}"; do
    if docker-compose ps $service | grep -q "Up"; then
        echo "✅ $service: Running"
    else
        echo "❌ $service: Not running"
    fi
done

# Check endpoints
endpoints=(
    "http://localhost:5678/healthz"
    "http://localhost:3001/health"
)

for endpoint in "${endpoints[@]}"; do
    if curl -f -s $endpoint > /dev/null; then
        echo "✅ $endpoint: Accessible"
    else
        echo "❌ $endpoint: Not accessible"
    fi
done

# Check disk space
df -h | grep -E "(/$|/var)" | awk '{print $5 " " $6}' | while read line; do
    usage=$(echo $line | awk '{print $1}' | sed 's/%//')
    partition=$(echo $line | awk '{print $2}')
    if [ $usage -gt 80 ]; then
        echo "⚠️  Disk usage high on $partition: $usage%"
    else
        echo "✅ Disk usage OK on $partition: $usage%"
    fi
done
```

### 2. Cleanup Script
```bash
#!/bin/bash
# cleanup.sh

echo "=== Cleaning up old data ==="

# Clean Docker resources
docker system prune -f
docker volume prune -f

# Clean old logs
find ./logs -name "*.log" -type f -mtime +7 -delete

# Clean old backups (keep last 30 days)
find ./backups -name "*.sql.gz" -type f -mtime +30 -delete

echo "Cleanup completed"
```

### 3. Backup Script
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="./backups"

echo "=== Creating backup: $DATE ==="

# Database backup
docker exec postgres_container pg_dump -U n8n n8n > $BACKUP_DIR/db_backup_$DATE.sql
gzip $BACKUP_DIR/db_backup_$DATE.sql

# Workflow backup
docker exec n8n_container n8n export:workflow --all --output=/tmp/workflows_$DATE.json
docker cp n8n_container:/tmp/workflows_$DATE.json $BACKUP_DIR/

# Upload to S3 (if configured)
if [ ! -z "$BACKUP_S3_BUCKET" ]; then
    aws s3 cp $BACKUP_DIR/db_backup_$DATE.sql.gz s3://$BACKUP_S3_BUCKET/
    aws s3 cp $BACKUP_DIR/workflows_$DATE.json s3://$BACKUP_S3_BUCKET/
fi

echo "Backup completed: $DATE"
```

## 📞 Emergency Procedures

### 1. Service Recovery
```bash
# Quick service restart
docker-compose restart

# Full system recovery
docker-compose down
docker-compose up -d

# Database recovery from backup
./scripts/restore-database.sh backup_file.sql.gz
```

### 2. Rollback Procedures
```bash
# Rollback to previous container versions
docker-compose down
docker tag n8nio/n8n:latest n8nio/n8n:backup
docker pull n8nio/n8n:previous-version
docker-compose up -d
```

### 3. Emergency Contacts
- System Administrator: <EMAIL>
- DevOps Team: <EMAIL>
- On-call Engineer: +1-555-0123

---

Regular maintenance and proactive monitoring ensure optimal performance and reliability of your automation suite.
