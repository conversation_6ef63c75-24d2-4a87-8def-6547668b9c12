# n8n Environment Configuration Template
# Copy this file to .env and update with your actual values

# ==============================================
# n8n Configuration
# ==============================================
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=your_secure_password_here

# Database Configuration
DB_TYPE=postgresdb
DB_POSTGRESDB_HOST=db
DB_POSTGRESDB_PORT=5432
DB_POSTGRESDB_DATABASE=n8n
DB_POSTGRESDB_USER=n8n
DB_POSTGRESDB_PASSWORD=your_db_password_here

# n8n Settings
NODE_FUNCTION_ALLOW_EXTERNAL=axios,qs,lodash,moment
GENERIC_TIMEZONE=America/New_York
N8N_SECURE_COOKIE=false
NODE_ENV=production

# ==============================================
# Strapi Configuration
# ==============================================

# Development Environment
STRAPI_DEV_URL=http://localhost:1337
STRAPI_DEV_TOKEN=your_strapi_dev_api_token_here

# Production Environment  
STRAPI_PROD_URL=https://your-production-strapi.com
STRAPI_PROD_TOKEN=your_strapi_prod_api_token_here

# ==============================================
# OpenAI Configuration
# ==============================================
OPENAI_API_KEY=sk-your_openai_api_key_here
OPENAI_ORG_ID=org-your_organization_id_here

# ==============================================
# MCP Server Configuration
# ==============================================
MCP_SERVER_HOST=localhost
MCP_SERVER_PORT=3001
MCP_SERVER_SECRET=your_mcp_server_secret_here

# ==============================================
# Email Configuration (for notifications)
# ==============================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password_here
SMTP_FROM=<EMAIL>

# ==============================================
# Webhook Configuration
# ==============================================
WEBHOOK_BASE_URL=http://localhost:5678
WEBHOOK_SECRET=your_webhook_secret_here

# ==============================================
# Logging and Monitoring
# ==============================================
LOG_LEVEL=info
ENABLE_METRICS=true
METRICS_PORT=9090

# ==============================================
# Security Settings
# ==============================================
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_32_character_encryption_key_here

# ==============================================
# Rate Limiting
# ==============================================
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# ==============================================
# Backup Configuration
# ==============================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_REGION=us-east-1
BACKUP_S3_ACCESS_KEY=your_s3_access_key
BACKUP_S3_SECRET_KEY=your_s3_secret_key
