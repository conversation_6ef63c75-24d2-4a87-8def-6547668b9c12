import { readdir } from 'fs';
import { join, dirname } from 'path';
import { createInterface } from 'readline';
import { exec } from 'child_process';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __dirname = dirname(fileURLToPath(import.meta.url));

// Create a readline interface
const rl = createInterface({
    input: process.stdin,
    output: process.stdout
});

// Read all files inside the src/scripts folder
const scriptsFolder = join(__dirname, 'src/scripts');
readdir(scriptsFolder, (err, files) => {
    if (err) {
        console.error('Error reading the scripts folder:', err);
        return;
    }

    // Filter JavaScript files
    const scriptFiles = files.filter(file => file.endsWith('.js'));

    // Create menu options based on the found files
    const menuOptions = scriptFiles.map((file, index) => ({
        option: (index + 1).toString(),
        description: `Run ${file}`,
        command: `node ${join(scriptsFolder, file)}`
    }));

    // Display the menu
    console.log('Please choose an option:');
    menuOptions.forEach(option => {
        console.log(`${option.option}. ${option.description}`);
    });

    // Capture user input
    rl.question('Enter your choice: ', (answer) => {
        const selectedOption = menuOptions.find(option => option.option === answer);

        if (selectedOption) {
            // Execute the corresponding command
            const child = exec(selectedOption.command);

            // Send console.log messages to the terminal
            child.stdout.on('data', (data) => {
                console.log(data.toString());
            });

            child.stderr.on('data', (data) => {
                console.error(data.toString());
            });

            child.on('close', (code) => {
                console.log(`Child process exited with code ${code}`);
                rl.close();
            });
        } else {
            console.log('Invalid choice. Please try again.');
        }

        // Close the readline interface
        rl.close();
    });
});